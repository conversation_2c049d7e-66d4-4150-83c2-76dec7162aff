﻿using LSB.LogMonitor.Service.Contracts;
using LSB.LogMonitor.Services;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using YamlDotNet.Serialization;
using YamlDotNet.Serialization.NamingConventions;

namespace LSB.LogMonitor.Services
{
    public class LogService : ILogService
    {
        private readonly ILogger<LogService> _logger;
        private readonly string _logRootPath = @"C:\ProgramData\DaBox\LSBHub\Logs";
        //private readonly string _logRootPath = @"C:\ProgramData\LSB\LSBCentral";

        private readonly IDeserializer _yamlDeserializer;

        // NEW: Regex for extracting business metrics from log messages
        private static readonly Regex NumberExtractRegex =
            new Regex(@"\b(\d+)\b", RegexOptions.Compiled | RegexOptions.IgnoreCase);

        public LogService(ILogger<LogService> logger)
        {
            _logger = logger;
            _yamlDeserializer = new DeserializerBuilder()
                .WithNamingConvention(CamelCaseNamingConvention.Instance)
                .Build();
        }

        public async Task<LogSummaryResponse> GenerateLogSummaryAsync(DateTime date, string clientName, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Generating log summary for client {ClientName} on {Date}", clientName, date.ToString("yyyy-MM-dd"));

            var response = new LogSummaryResponse
            {
                ClientName = clientName,
                Date = date,
                LogTypeSummaries = new List<LogSummary>(),
                LogFiles = new List<LogFileInfo>(),
                LogTypeDistribution = new Dictionary<string, int>(),
                SourceNameDistribution = new Dictionary<string, int>()
            };

            var clientPath = Path.Combine(_logRootPath, clientName);
            var datePath = Path.Combine(clientPath, date.ToString("yyyy-MM-dd"));

            if (!Directory.Exists(datePath))
            {
                _logger.LogWarning("Log directory not found: {Path}", datePath);
                return response;
            }

            var logFiles = Directory.GetFiles(datePath, "*.log");
            if (logFiles.Length == 0)
            {
                _logger.LogWarning("No log files found in: {Path}", datePath);
                return response;
            }

            var logEntries = new List<LogEntry>();
            var logTypeCounts = new Dictionary<string, int>();
            var sourceNameCounts = new Dictionary<string, int>();
            var logTypeMessages = new Dictionary<string, List<string>>();
            var logTypeFirstOccurrence = new Dictionary<string, DateTime>();
            var logTypeLastOccurrence = new Dictionary<string, DateTime>();
            var sourceNameFirstOccurrence = new Dictionary<string, DateTime>();
            var sourceNameLastOccurrence = new Dictionary<string, DateTime>();

            int totalEntries = 0;

            foreach (var logFile in logFiles)
            {
                var fileName = Path.GetFileName(logFile);
                var fileEntries = new List<LogEntry>();
                DateTime? firstEntryTime = null;
                DateTime? lastEntryTime = null;

                try
                {
                    var logContent = await File.ReadAllTextAsync(logFile, cancellationToken);

                    // Skip empty files
                    if (string.IsNullOrWhiteSpace(logContent))
                    {
                        _logger.LogInformation("Empty log file: {FileName}", fileName);
                        continue;
                    }

                    var entries = ParseYamlLogEntries(logContent);

                    if (entries.Any())
                    {
                        fileEntries.AddRange(entries);
                        firstEntryTime = entries.Min(e => e.Time);
                        lastEntryTime = entries.Max(e => e.Time);
                        totalEntries += entries.Count;

                        // Process log type statistics
                        foreach (var entry in entries)
                        {
                            // Count by log type
                            if (!logTypeCounts.ContainsKey(entry.LogType))
                            {
                                logTypeCounts[entry.LogType] = 0;
                                logTypeMessages[entry.LogType] = new List<string>();
                                logTypeFirstOccurrence[entry.LogType] = entry.Time;
                                logTypeLastOccurrence[entry.LogType] = entry.Time;
                            }

                            logTypeCounts[entry.LogType]++;

                            if (entry.Time < logTypeFirstOccurrence[entry.LogType])
                                logTypeFirstOccurrence[entry.LogType] = entry.Time;

                            if (entry.Time > logTypeLastOccurrence[entry.LogType])
                                logTypeLastOccurrence[entry.LogType] = entry.Time;

                            // Keep sample messages (up to 5 per log type)
                            if (logTypeMessages[entry.LogType].Count < 5 && !string.IsNullOrEmpty(entry.Message))
                                logTypeMessages[entry.LogType].Add(entry.Message);

                            // Count by source name
                            var sourceName = !string.IsNullOrEmpty(entry.SourceName) ? entry.SourceName : "Unknown";
                            if (!sourceNameCounts.ContainsKey(sourceName))
                            {
                                sourceNameCounts[sourceName] = 0;
                                sourceNameFirstOccurrence[sourceName] = entry.Time;
                                sourceNameLastOccurrence[sourceName] = entry.Time;
                            }

                            sourceNameCounts[sourceName]++;

                            if (entry.Time < sourceNameFirstOccurrence[sourceName])
                                sourceNameFirstOccurrence[sourceName] = entry.Time;

                            if (entry.Time > sourceNameLastOccurrence[sourceName])
                                sourceNameLastOccurrence[sourceName] = entry.Time;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing log file: {FileName}", fileName);
                }

                response.LogFiles.Add(new LogFileInfo
                {
                    FileName = fileName,
                    TotalEntries = fileEntries.Count,
                    FirstEntryTime = firstEntryTime,
                    LastEntryTime = lastEntryTime
                });

                logEntries.AddRange(fileEntries);
            }

            // Create log type summaries
            foreach (var logType in logTypeCounts.Keys)
            {
                response.LogTypeSummaries.Add(new LogSummary
                {
                    LogType = logType,
                    Count = logTypeCounts[logType],
                    FirstOccurrence = logTypeFirstOccurrence[logType],
                    LastOccurrence = logTypeLastOccurrence[logType],
                    SampleMessages = logTypeMessages[logType]
                });
            }

            // Set total entries and distributions
            response.TotalLogEntries = totalEntries;
            response.LogTypeDistribution = logTypeCounts;
            response.SourceNameDistribution = sourceNameCounts;

            // Generate health summary with business metrics
            response.HealthSummary = GenerateHealthSummary(logEntries, logFiles);

            return response;
        }

        public async Task<bool> LogsExistAsync(DateTime date, string clientName, CancellationToken cancellationToken = default)
        {
            var clientPath = Path.Combine(_logRootPath, clientName);
            var datePath = Path.Combine(clientPath, date.ToString("yyyy-MM-dd"));

            if (!Directory.Exists(datePath))
                return false;

            var logFiles = Directory.GetFiles(datePath, "*.log");
            return logFiles.Length > 0;
        }

        public Task<string[]> GetAvailableClientNamesAsync(CancellationToken cancellationToken = default)
        {
            if (!Directory.Exists(_logRootPath))
                return Task.FromResult(Array.Empty<string>());

            var clientDirectories = Directory.GetDirectories(_logRootPath);
            var clientNames = clientDirectories.Select(Path.GetFileName).ToArray();

            return Task.FromResult(clientNames);
        }

        private List<LogEntry> ParseYamlLogEntries(string content)
        {
            var entries = new List<LogEntry>();

            try
            {
                // Manual parsing approach for the specific YAML format
                var lines = content.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);

                LogEntry? currentEntry = null;

                foreach (var line in lines)
                {
                    var trimmedLine = line.Trim();

                    // Start of a new log entry
                    if (trimmedLine == "- !Log")
                    {
                        // Save previous entry if exists
                        if (currentEntry != null)
                        {
                            entries.Add(currentEntry);
                        }

                        // Create new entry
                        currentEntry = new LogEntry();
                    }
                    // Parse log entry properties
                    else if (currentEntry != null && trimmedLine.Contains(":"))
                    {
                        var colonIndex = trimmedLine.IndexOf(':');
                        if (colonIndex > 0)
                        {
                            var key = trimmedLine.Substring(0, colonIndex).Trim();
                            var value = trimmedLine.Substring(colonIndex + 1).Trim();

                            switch (key)
                            {
                                case "LogType":
                                    currentEntry.LogType = value;
                                    break;
                                case "Time":
                                    if (DateTime.TryParse(value, out var time))
                                    {
                                        currentEntry.Time = time;
                                    }
                                    break;
                                case "Message":
                                    // Handle multi-line messages with the >- YAML syntax
                                    if (value.StartsWith(">-"))
                                    {
                                        currentEntry.Message = value.Substring(2).Trim();

                                        // Check for common patterns in multi-line messages
                                        if ((currentEntry.Message.Contains("Exception:") ||
                                             currentEntry.Message.Contains("Error:") ||
                                             currentEntry.Message.Contains("Forbidden") ||
                                             currentEntry.Message.Contains("Invalid object name") ||
                                             currentEntry.Message.Contains("SendAsyncCore") ||
                                             currentEntry.Message.Contains("Authority_AccessTokenFromRefreshToken")) &&
                                            currentEntry.LogType != "Error")
                                        {
                                            // If message contains exception but log type is not Error, change it to Error
                                            currentEntry.LogType = "Error";
                                        }

                                        if (currentEntry.Message.StartsWith("[DEBUG]") && currentEntry.LogType != "Debug")
                                        {
                                            // If message starts with [DEBUG] but log type is not Debug, change it to Debug
                                            currentEntry.LogType = "Debug";
                                        }

                                        if ((currentEntry.Message.Contains("Install LSB Hub Service") ||
                                             currentEntry.Message.Contains("Service LSB Hub Service is started") ||
                                             currentEntry.Message.Contains("Get the config")) &&
                                            currentEntry.LogType != "Info")
                                        {
                                            // If message contains service info but log type is not Info, change it to Info
                                            currentEntry.LogType = "Info";
                                        }
                                    }
                                    else
                                    {
                                        currentEntry.Message = value;

                                        // Check for common patterns in single-line messages
                                        if ((value.Contains("FAILED") ||
                                             value.Contains("Error") ||
                                             value.Contains("Exception") ||
                                             value.Contains("Forbidden") ||
                                             value.Contains("Invalid object name")) &&
                                            currentEntry.LogType != "Error")
                                        {
                                            // If message contains error indicators but log type is not Error, change it to Error
                                            currentEntry.LogType = "Error";
                                        }

                                        if (value.Contains("Stop service") || value.Contains("is Stopped") || value.Contains("stopping"))
                                        {
                                            // Track service status information
                                            if (currentEntry.LogType != "Warning")
                                            {
                                                currentEntry.LogType = "Warning";
                                            }
                                        }

                                        if ((value.Contains("PROCEED") ||
                                             value.Contains("RUN Rule") ||
                                             value.Contains("excecute per") ||
                                             value.Contains("Processing") ||
                                             value.Contains("Service is started")) &&
                                            currentEntry.LogType != "Info")
                                        {
                                            // If message contains normal operation indicators but log type is not Info, change it to Info
                                            currentEntry.LogType = "Info";
                                        }
                                    }
                                    break;
                                case "SourceName":
                                    currentEntry.SourceName = value;
                                    break;
                            }
                        }
                    }
                }

                // Add the last entry if exists
                if (currentEntry != null)
                {
                    entries.Add(currentEntry);
                }

                _logger.LogInformation("Parsed {Count} log entries from content with {Lines} lines", entries.Count, lines.Length);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error parsing log content");
            }

            return entries;
        }

        private class LogEntry
        {
            public string LogType { get; set; } = string.Empty;
            public DateTime Time { get; set; }
            public string Message { get; set; } = string.Empty;
            public string SourceName { get; set; } = string.Empty;
        }

        private SystemHealthSummary GenerateHealthSummary(List<LogEntry> logEntries, string[] logFiles)
        {
            var healthSummary = new SystemHealthSummary();
            var serviceEntries = new Dictionary<string, List<LogEntry>>();
            var allErrorPatterns = new Dictionary<string, ErrorPattern>();

            // Current timestamp for freshness calculations
            var now = DateTime.Now;

            // If there are no log entries, create default service entries for each log file
            if (!logEntries.Any() && logFiles.Any())
            {
                foreach (var logFile in logFiles)
                {
                    var serviceName = Path.GetFileNameWithoutExtension(logFile);
                    var serviceHealth = new ServiceHealthInfo
                    {
                        ServiceName = serviceName,
                        Status = "Unknown",
                        IsRunning = false,
                        // Initialize senior metrics for empty services
                        ServiceErrorsPerMinute = 0,
                        ServiceTimeSinceLastLog = TimeSpan.MaxValue,
                        IsServiceStale = true,
                        ServiceUptimePercentage = 0.0,
                        // NEW: Initialize business metrics for empty services
                        LastReceivedKeywordReports = 0,
                        LastReceivedSqsConversions = 0,
                        LastReceivedSqsTraffics = 0
                    };

                    healthSummary.Services.Add(serviceHealth);
                }

                // Set basic health summary properties
                healthSummary.TotalServices = healthSummary.Services.Count;
                healthSummary.RunningServices = 0;
                healthSummary.StoppedServices = healthSummary.TotalServices;
                healthSummary.AllServicesRunning = false;
                healthSummary.OverallStatus = "Unknown";

                // Initialize senior metrics for empty system
                healthSummary.ErrorsPerMinute = 0;
                healthSummary.IsErrorSpike = false;
                healthSummary.LastErrorSpikeTime = null;
                healthSummary.ServiceUptimePercentage = 0.0;
                healthSummary.CurrentDowntime = TimeSpan.FromDays(1); // Assume full day downtime if no logs
                healthSummary.TimeSinceLastLog = TimeSpan.MaxValue;
                healthSummary.IsLogStale = true;

                // NEW: Initialize business metrics for empty system
                healthSummary.TotalKeywordReports = 0;
                healthSummary.TotalSqsConversions = 0;
                healthSummary.TotalSqsTraffics = 0;

                return healthSummary;
            }

            // Calculate system-wide senior metrics first
            CalculateSystemSeniorMetrics(healthSummary, logEntries, now);

            // NEW: Calculate business metrics from log entries
            CalculateBusinessMetrics(healthSummary, logEntries, now);

            // Define known error patterns
            var knownErrorPatterns = new List<(string pattern, string description, string severity, bool isUserImpacting, string recommendedAction)>
            {
                ("Execution Timeout Expired", "Database query timeout - The SQL query took too long to execute", "High", true,
                 "Review and optimize SQL queries, consider adding indexes or breaking down large queries"),

                ("startDate to endDate range", "API date range limit exceeded - The requested date range exceeds the API's maximum allowed range", "Medium", true,
                 "Modify the date range to be 31 days or less, or split the request into multiple smaller ranges"),

                ("OutOfMemory", "Out of memory exception - The application has exhausted available memory", "Critical", true,
                 "Increase available memory, optimize memory usage, or check for memory leaks"),

                ("Connection", "Connection-related issue - Problem with network or service connectivity", "High", true,
                 "Check network connectivity, ensure services are running, and verify connection strings"),

                ("The wait operation timed out", "Operation timeout - A wait operation has timed out", "Medium", true,
                 "Review long-running operations and consider implementing timeouts or retry logic"),

                ("Response status code does not indicate success", "API request failed - The API returned an error response", "Medium", true,
                 "Check API request parameters and handle error responses appropriately"),

                ("FAILED ReportTask", "Report task failure - A scheduled report task has failed", "High", true,
                 "Check the report parameters and verify API access permissions"),

                ("Stop service", "Service stopped - A service has been stopped", "Medium", false,
                 "Verify if the service was stopped intentionally or unexpectedly"),

                ("is Stopped", "Service stopped status - A service is in stopped state", "Medium", false,
                 "Check if the service needs to be restarted"),

                ("Services is stopping", "Service stopping - A service is in the process of stopping", "Low", false,
                 "Monitor to ensure the service stops cleanly"),

                ("Win32Exception", "Windows system exception - A Windows-specific error has occurred", "High", true,
                 "Check Windows event logs for more details about the system error"),

                ("ComponentModel.Win32Exception", "Windows component exception - A Windows component has encountered an error", "High", true,
                 "Check Windows event logs and component status"),

                ("Bad Request", "Bad API request - The request to an API was malformed or invalid", "Medium", true,
                 "Review the API request parameters and format"),

                ("(403) Forbidden", "Access forbidden - The API request was denied due to insufficient permissions", "High", true,
                 "Check API credentials and permissions, verify that the access token is valid"),

                ("Invalid object name", "Database object not found - A database query referenced a non-existent object", "High", true,
                 "Check database schema and ensure all required tables and objects exist"),

                ("SendAsyncCore", "HTTP request failure - Failed to send HTTP request", "Medium", true,
                 "Check network connectivity and endpoint availability"),

                ("Authority_AccessTokenFromRefreshToken", "Token refresh failure - Failed to refresh the access token", "High", true,
                 "Check refresh token validity and API credentials"),

                ("Install LSB Hub Service", "Service installation - The LSB Hub Service is being installed", "Info", false,
                 "No action required, this is an informational message"),

                ("Service LSB Hub Service is started", "Service started - The LSB Hub Service has been started", "Info", false,
                 "No action required, this is an informational message"),

                ("Get the config", "Configuration loading - The service is loading its configuration", "Info", false,
                 "No action required, this is an informational message"),

                // NEW: Business metrics related patterns
                ("KeywordReportService", "Keyword report processing issue", "High", true,
                 "Check keyword report API and data pipeline"),

                ("SqsConversionProcessor", "SQS conversion processing failed", "High", true,
                 "Verify SQS conversion queue processing"),

                ("SqsTrafficProcessor", "SQS traffic processing failed", "High", true,
                 "Check SQS traffic data pipeline"),

                ("API rate limit", "API rate limit exceeded", "Medium", true,
                 "Implement rate limiting and retry logic"),

                ("Queue empty", "Processing queue is empty", "Low", false,
                 "Monitor queue population")
            };

            // Group log entries by service/source name
            foreach (var entry in logEntries)
            {
                var serviceName = !string.IsNullOrEmpty(entry.SourceName) ? entry.SourceName : Path.GetFileNameWithoutExtension(logFiles.FirstOrDefault(f => f.Contains(entry.SourceName)) ?? "Unknown");

                if (!serviceEntries.ContainsKey(serviceName))
                {
                    serviceEntries[serviceName] = new List<LogEntry>();
                }

                serviceEntries[serviceName].Add(entry);

                // Process error entries for pattern matching
                if (entry.LogType.Equals("Error", StringComparison.OrdinalIgnoreCase) && !string.IsNullOrEmpty(entry.Message))
                {
                    // Check against known error patterns
                    foreach (var (pattern, description, severity, isUserImpacting, recommendedAction) in knownErrorPatterns)
                    {
                        if (entry.Message.Contains(pattern, StringComparison.OrdinalIgnoreCase))
                        {
                            string patternKey = $"{serviceName}:{pattern}";

                            if (!allErrorPatterns.ContainsKey(patternKey))
                            {
                                allErrorPatterns[patternKey] = new ErrorPattern
                                {
                                    Pattern = pattern,
                                    Description = description,
                                    Count = 0,
                                    Severity = severity,
                                    FirstOccurrence = entry.Time,
                                    LastOccurrence = entry.Time,
                                    SampleMessages = new List<string>(),
                                    IsUserImpacting = isUserImpacting,
                                    RecommendedAction = recommendedAction
                                };
                            }

                            var errorPattern = allErrorPatterns[patternKey];
                            errorPattern.Count++;

                            if (entry.Time < errorPattern.FirstOccurrence)
                                errorPattern.FirstOccurrence = entry.Time;

                            if (entry.Time > errorPattern.LastOccurrence)
                                errorPattern.LastOccurrence = entry.Time;

                            if (errorPattern.SampleMessages.Count < 3 && !errorPattern.SampleMessages.Contains(entry.Message))
                                errorPattern.SampleMessages.Add(entry.Message);

                            break; // Match the first pattern only
                        }
                    }
                }
            }

            // Process each service
            foreach (var serviceName in serviceEntries.Keys)
            {
                var entries = serviceEntries[serviceName];
                var serviceHealth = new ServiceHealthInfo
                {
                    ServiceName = serviceName,
                    StartTime = entries.Where(e => e.Message.Contains("start", StringComparison.OrdinalIgnoreCase) ||
                                                e.Message.Contains("init", StringComparison.OrdinalIgnoreCase))
                                      .OrderBy(e => e.Time)
                                      .FirstOrDefault()?.Time,
                    LastActivityTime = entries.Max(e => e.Time),
                    ErrorCount = entries.Count(e => e.LogType.Equals("Error", StringComparison.OrdinalIgnoreCase)),
                    WarningCount = entries.Count(e => e.LogType.Equals("Warning", StringComparison.OrdinalIgnoreCase)),
                    InfoCount = entries.Count(e => e.LogType.Equals("Info", StringComparison.OrdinalIgnoreCase)),
                };

                // Calculate per-service senior metrics
                CalculateServiceSeniorMetrics(serviceHealth, entries, now);

                // NEW: Calculate per-service business metrics
                CalculateServiceBusinessMetrics(serviceHealth, entries, now);

                // Check if service is running based on log patterns
                var lastEntry = entries.OrderByDescending(e => e.Time).FirstOrDefault();
                var hasStopMessage = entries.Any(e => e.Message.Contains("stop", StringComparison.OrdinalIgnoreCase) ||
                                                    e.Message.Contains("shutdown", StringComparison.OrdinalIgnoreCase) ||
                                                    e.Message.Contains("terminated", StringComparison.OrdinalIgnoreCase) ||
                                                    e.Message.Contains("is Stopped", StringComparison.OrdinalIgnoreCase));

                // Check for explicit running messages
                bool isRunningMessage = lastEntry?.Message?.Contains("running", StringComparison.OrdinalIgnoreCase) ?? false;
                bool isStartedMessage = entries.Any(e => e.Message.Contains("Service is started", StringComparison.OrdinalIgnoreCase) &&
                                                    e.Time > DateTime.Now.AddHours(-24));

                // Check for regular activity
                bool hasRecentActivity = lastEntry != null &&
                                        lastEntry.Time > DateTime.Now.AddHours(-24) &&
                                        lastEntry.LogType?.Equals("Info", StringComparison.OrdinalIgnoreCase) == true;

                // Check for debug messages indicating regular execution
                bool hasDebugActivity = entries.Any(e => (e.Message.Contains("excecute per", StringComparison.OrdinalIgnoreCase) ||
                                                        e.Message.Contains("RUN Rule", StringComparison.OrdinalIgnoreCase) ||
                                                        e.Message.Contains("PROCEED", StringComparison.OrdinalIgnoreCase) ||
                                                        e.Message.Contains("Processing", StringComparison.OrdinalIgnoreCase)) &&
                                                    e.Time > DateTime.Now.AddHours(-24));

                // Check for service installation messages
                bool wasRecentlyInstalled = entries.Any(e => e.Message.Contains("Install LSB Hub Service", StringComparison.OrdinalIgnoreCase) &&
                                                        e.Time > DateTime.Now.AddHours(-24));

                // Service is running if it has no stop messages and either has explicit running messages or recent activity
                serviceHealth.IsRunning = !hasStopMessage && (isRunningMessage || isStartedMessage || hasRecentActivity || hasDebugActivity || wasRecentlyInstalled);

                // Get recent errors and warnings
                serviceHealth.RecentErrors = entries.Where(e => e.LogType.Equals("Error", StringComparison.OrdinalIgnoreCase))
                                                  .OrderByDescending(e => e.Time)
                                                  .Take(5)
                                                  .Select(e => e.Message)
                                                  .ToList();

                serviceHealth.RecentWarnings = entries.Where(e => e.LogType.Equals("Warning", StringComparison.OrdinalIgnoreCase))
                                                    .OrderByDescending(e => e.Time)
                                                    .Take(5)
                                                    .Select(e => e.Message)
                                                    .ToList();

                // Add error patterns for this service
                serviceHealth.CommonErrorPatterns = allErrorPatterns
                    .Where(kv => kv.Key.StartsWith($"{serviceName}:"))
                    .Select(kv => kv.Value)
                    .OrderByDescending(p => p.Count)
                    .ToList();

                // Determine service status based on errors and their severity
                if (serviceHealth.ErrorCount > 0)
                {
                    bool serviceCriticalErrors = serviceHealth.CommonErrorPatterns.Any(p => p.Severity == "Critical");
                    bool serviceHighSeverityErrors = serviceHealth.CommonErrorPatterns.Any(p => p.Severity == "High");

                    if (serviceCriticalErrors)
                        serviceHealth.Status = "Critical";
                    else if (serviceHighSeverityErrors)
                        serviceHealth.Status = serviceHealth.IsRunning ? "Warning" : "Critical";
                    else
                        serviceHealth.Status = serviceHealth.IsRunning ? "Warning" : "Critical";
                }
                else if (serviceHealth.WarningCount > 0)
                {
                    serviceHealth.Status = "Warning";
                }
                else if (serviceHealth.IsRunning)
                {
                    // Check for recent activity to determine if service is healthy or just idle
                    var lastActivity = entries.OrderByDescending(e => e.Time).FirstOrDefault()?.Time;
                    bool hasVeryRecentActivity = lastActivity.HasValue && lastActivity.Value > DateTime.Now.AddHours(-1);
                    bool hasRegularActivity = entries.Count(e => e.Time > DateTime.Now.AddHours(-6)) >= 3; // At least 3 entries in the last 6 hours

                    // Check for specific activity patterns
                    bool hasRuleExecution = entries.Any(e => (e.Message.Contains("RUN Rule", StringComparison.OrdinalIgnoreCase) ||
                                                            e.Message.Contains("PROCEED", StringComparison.OrdinalIgnoreCase)) &&
                                                        e.Time > DateTime.Now.AddHours(-3));

                    bool hasProcessingActivity = entries.Any(e => (e.Message.Contains("Processing", StringComparison.OrdinalIgnoreCase) ||
                                                                e.Message.Contains("excecute per", StringComparison.OrdinalIgnoreCase)) &&
                                                            e.Time > DateTime.Now.AddHours(-3));

                    if (hasVeryRecentActivity && (hasRuleExecution || hasProcessingActivity || hasRegularActivity))
                    {
                        serviceHealth.Status = "Healthy";
                    }
                    else if (hasVeryRecentActivity)
                    {
                        serviceHealth.Status = "Active";
                    }
                    else if (lastActivity.HasValue && lastActivity.Value > DateTime.Now.AddHours(-6))
                    {
                        serviceHealth.Status = "Idle";
                    }
                    else if (lastActivity.HasValue && lastActivity.Value > DateTime.Now.AddHours(-24))
                    {
                        serviceHealth.Status = "Stale";
                    }
                    else
                    {
                        serviceHealth.Status = "Inactive";
                    }
                }
                else if (entries.Any(e => e.Message.Contains("Stop service", StringComparison.OrdinalIgnoreCase) ||
                                       e.Message.Contains("is Stopped", StringComparison.OrdinalIgnoreCase)))
                {
                    // Explicitly stopped service
                    serviceHealth.Status = "Stopped";
                }
                else
                {
                    // No activity and no explicit stop message
                    serviceHealth.Status = "Unknown";
                }

                healthSummary.Services.Add(serviceHealth);
            }

            // Calculate overall health summary
            healthSummary.TotalServices = healthSummary.Services.Count;
            healthSummary.RunningServices = healthSummary.Services.Count(s => s.IsRunning);
            healthSummary.StoppedServices = healthSummary.Services.Count(s => s.Status == "Stopped");
            healthSummary.IdleServices = healthSummary.Services.Count(s => s.Status == "Idle");
            healthSummary.StaleServices = healthSummary.Services.Count(s => s.Status == "Stale");
            healthSummary.ActiveServices = healthSummary.Services.Count(s => s.Status == "Active");
            healthSummary.InactiveServices = healthSummary.Services.Count(s => s.Status == "Inactive");
            healthSummary.CriticalServices = healthSummary.Services.Count(s => s.Status == "Critical");
            healthSummary.WarningServices = healthSummary.Services.Count(s => s.Status == "Warning");
            healthSummary.HealthyServices = healthSummary.Services.Count(s => s.Status == "Healthy");
            healthSummary.AllServicesRunning = healthSummary.RunningServices == healthSummary.TotalServices;
            healthSummary.TotalErrors = healthSummary.Services.Sum(s => s.ErrorCount);
            healthSummary.TotalWarnings = healthSummary.Services.Sum(s => s.WarningCount);
            healthSummary.LastUpdated = DateTime.Now;

            // Build service status distribution
            var statusDistribution = healthSummary.Services
                .GroupBy(s => s.Status)
                .ToDictionary(g => g.Key, g => g.Count());
            healthSummary.ServiceStatusDistribution = statusDistribution;

            // Track last activity by service
            healthSummary.LastActivityByService = healthSummary.Services
                .ToDictionary(s => s.ServiceName, s => serviceEntries.ContainsKey(s.ServiceName) ?
                    serviceEntries[s.ServiceName].OrderByDescending(e => e.Time).FirstOrDefault()?.Time : null);

            // Compile most common and severe errors
            var allPatterns = allErrorPatterns.Values.ToList();
            healthSummary.TotalUniqueErrorPatterns = allPatterns.Count;

            healthSummary.MostCommonErrors = allPatterns
                .OrderByDescending(p => p.Count)
                .Take(5)
                .ToList();

            healthSummary.MostSevereErrors = allPatterns
                .Where(p => p.Severity == "Critical" || p.Severity == "High")
                .OrderByDescending(p => p.Severity == "Critical" ? 1 : 0)
                .ThenByDescending(p => p.Count)
                .Take(5)
                .ToList();

            healthSummary.UserImpactingErrors = allPatterns
                .Where(p => p.IsUserImpacting)
                .OrderByDescending(p => p.Count)
                .Take(5)
                .ToList();

            // Finalize system-wide service uptime calculation
            CalculateSystemUptimeMetrics(healthSummary);

            // NEW: Finalize business metrics summary
            FinalizeBusinessMetricsSummary(healthSummary);

            // Enhanced overall status determination including business metrics
            DetermineOverallStatusWithBusinessMetrics(healthSummary, allPatterns);

            return healthSummary;
        }

        /// <summary>
        /// Calculate system-wide senior metrics for error velocity and log freshness
        /// </summary>
        private void CalculateSystemSeniorMetrics(SystemHealthSummary healthSummary, List<LogEntry> logEntries, DateTime now)
        {
            // 1. LOG FRESHNESS - Find most recent log across all services
            var mostRecentLogTime = logEntries.Any() ? logEntries.Max(e => e.Time) : (DateTime?)null;

            if (mostRecentLogTime.HasValue)
            {
                healthSummary.TimeSinceLastLog = now - mostRecentLogTime.Value;
                healthSummary.IsLogStale = healthSummary.TimeSinceLastLog.TotalMinutes > 30;
            }
            else
            {
                healthSummary.TimeSinceLastLog = TimeSpan.MaxValue;
                healthSummary.IsLogStale = true;
            }

            // 2. ERROR VELOCITY - Calculate errors per minute in last 5 minutes
            var fiveMinutesAgo = now.AddMinutes(-5);
            var recentErrors = logEntries.Where(e =>
                e.LogType.Equals("Error", StringComparison.OrdinalIgnoreCase) &&
                e.Time >= fiveMinutesAgo).ToList();

            healthSummary.ErrorsPerMinute = recentErrors.Count / 5.0;

            // 3. ERROR SPIKE DETECTION - 10+ errors in 5 minutes is considered a spike
            healthSummary.IsErrorSpike = recentErrors.Count >= 10;

            // Track when the spike occurred
            if (healthSummary.IsErrorSpike)
            {
                healthSummary.LastErrorSpikeTime = recentErrors.Max(e => e.Time);
            }
            else
            {
                healthSummary.LastErrorSpikeTime = null;
            }

            _logger.LogDebug("System metrics calculated: ErrorsPerMinute={ErrorsPerMinute}, IsErrorSpike={IsErrorSpike}, TimeSinceLastLog={TimeSinceLastLog}min",
                healthSummary.ErrorsPerMinute,
                healthSummary.IsErrorSpike,
                healthSummary.TimeSinceLastLog.TotalMinutes);
        }

        /// <summary>
        /// Calculate per-service senior metrics including error rates and log freshness
        /// </summary>
        private void CalculateServiceSeniorMetrics(ServiceHealthInfo serviceHealth, List<LogEntry> serviceEntries, DateTime now)
        {
            // 1. PER-SERVICE ERROR VELOCITY - Calculate errors per minute for this service in last 5 minutes
            var fiveMinutesAgo = now.AddMinutes(-5);
            var serviceRecentErrors = serviceEntries.Where(e =>
                e.LogType.Equals("Error", StringComparison.OrdinalIgnoreCase) &&
                e.Time >= fiveMinutesAgo).ToList();

            serviceHealth.ServiceErrorsPerMinute = serviceRecentErrors.Count / 5.0;

            // 2. PER-SERVICE LOG FRESHNESS - Time since last log for this service
            var serviceLastLog = serviceEntries.Any() ? serviceEntries.Max(e => e.Time) : (DateTime?)null;

            if (serviceLastLog.HasValue)
            {
                serviceHealth.ServiceTimeSinceLastLog = now - serviceLastLog.Value;
                serviceHealth.IsServiceStale = serviceHealth.ServiceTimeSinceLastLog.TotalMinutes > 30;
            }
            else
            {
                serviceHealth.ServiceTimeSinceLastLog = TimeSpan.MaxValue;
                serviceHealth.IsServiceStale = true;
            }

            // 3. PER-SERVICE UPTIME CALCULATION (simplified based on running status and activity)
            if (serviceHealth.IsRunning && !serviceHealth.IsServiceStale)
            {
                // Service is running and has recent logs - assume good uptime
                serviceHealth.ServiceUptimePercentage = 100.0;
            }
            else if (serviceHealth.IsRunning && serviceHealth.IsServiceStale)
            {
                // Service claims to be running but logs are stale - degraded uptime
                serviceHealth.ServiceUptimePercentage = 75.0;
            }
            else if (!serviceHealth.IsRunning && !serviceHealth.IsServiceStale)
            {
                // Service is not running but has recent logs - might be restarting
                serviceHealth.ServiceUptimePercentage = 50.0;
            }
            else
            {
                // Service is not running and no recent logs - assume down
                serviceHealth.ServiceUptimePercentage = 0.0;
            }

            _logger.LogDebug("Service {ServiceName} metrics: ErrorsPerMinute={ErrorsPerMinute}, Uptime={Uptime}%, Stale={IsStale}",
                serviceHealth.ServiceName,
                serviceHealth.ServiceErrorsPerMinute,
                serviceHealth.ServiceUptimePercentage,
                serviceHealth.IsServiceStale);
        }

        /// <summary>
        /// Calculate system-wide uptime metrics and downtime estimates
        /// </summary>
        private void CalculateSystemUptimeMetrics(SystemHealthSummary healthSummary)
        {
            // 4. SYSTEM-WIDE UPTIME - Average uptime across all services
            if (healthSummary.Services.Any())
            {
                healthSummary.ServiceUptimePercentage = healthSummary.Services.Average(s => s.ServiceUptimePercentage);
            }
            else
            {
                healthSummary.ServiceUptimePercentage = 0.0;
            }

            // 5. CURRENT DOWNTIME ESTIMATION - Based on stopped and stale services
            var downServices = healthSummary.Services.Count(s => !s.IsRunning || s.IsServiceStale);
            var totalServices = healthSummary.Services.Count;

            if (downServices > 0 && totalServices > 0)
            {
                // Estimate downtime based on percentage of services down
                var downServiceRatio = (double)downServices / totalServices;

                // If more than 50% services are down, consider significant system downtime
                if (downServiceRatio > 0.5)
                {
                    healthSummary.CurrentDowntime = TimeSpan.FromMinutes(downServiceRatio * 60); // Scale with ratio
                }
                else
                {
                    healthSummary.CurrentDowntime = TimeSpan.FromMinutes(downServices * 5); // 5 min per down service
                }
            }
            else
            {
                healthSummary.CurrentDowntime = TimeSpan.Zero;
            }

            _logger.LogDebug("System uptime metrics: ServiceUptimePercentage={Uptime}%, CurrentDowntime={Downtime}min",
                healthSummary.ServiceUptimePercentage,
                healthSummary.CurrentDowntime.TotalMinutes);
        }

        /// <summary>
        /// NEW: Calculate business metrics from log entries
        /// </summary>
        private void CalculateBusinessMetrics(SystemHealthSummary healthSummary, List<LogEntry> logEntries, DateTime now)
        {
            var today = now.Date;
            var todayEntries = logEntries.Where(e => e.Time.Date == today).ToList();

            // Extract business metrics from log messages
            foreach (var entry in todayEntries)
            {
                var message = entry.Message?.ToLower() ?? "";

                // Look for keyword report indicators
                if (message.Contains("keyword report") || message.Contains("report generated") ||
                    message.Contains("keyword data processed") || message.Contains("keyword") && message.Contains("processed"))
                {
                    // Try to extract count from message
                    var count = ExtractCountFromMessage(entry.Message, "report");
                    healthSummary.TotalKeywordReports += count;
                }

                // Look for SQS conversion indicators
                if (message.Contains("sqs conversion") || message.Contains("conversion processed") ||
                    message.Contains("conversion data received") || message.Contains("conversion") && message.Contains("processed"))
                {
                    var count = ExtractCountFromMessage(entry.Message, "conversion");
                    healthSummary.TotalSqsConversions += count;
                }

                // Look for SQS traffic indicators
                if (message.Contains("sqs traffic") || message.Contains("traffic processed") ||
                    message.Contains("traffic data received") || message.Contains("traffic") && message.Contains("processed"))
                {
                    var count = ExtractCountFromMessage(entry.Message, "traffic");
                    healthSummary.TotalSqsTraffics += count;
                }
            }

            _logger.LogDebug("Business metrics calculated: Keywords={Keywords}, Conversions={Conversions}, Traffics={Traffics}",
                healthSummary.TotalKeywordReports, healthSummary.TotalSqsConversions, healthSummary.TotalSqsTraffics);
        }

        /// <summary>
        /// NEW: Calculate per-service business metrics
        /// </summary>
        private void CalculateServiceBusinessMetrics(ServiceHealthInfo serviceHealth, List<LogEntry> serviceEntries, DateTime now)
        {
            var today = now.Date;
            var todayEntries = serviceEntries.Where(e => e.Time.Date == today).ToList();

            foreach (var entry in todayEntries)
            {
                var message = entry.Message?.ToLower() ?? "";

                // Service-specific keyword reports
                if (message.Contains("keyword report") || message.Contains("report generated") ||
                    message.Contains("keyword") && message.Contains("processed"))
                {
                    var count = ExtractCountFromMessage(entry.Message, "report");
                    serviceHealth.LastReceivedKeywordReports += count;
                }

                // Service-specific SQS conversions
                if (message.Contains("sqs conversion") || message.Contains("conversion processed") ||
                    message.Contains("conversion") && message.Contains("processed"))
                {
                    var count = ExtractCountFromMessage(entry.Message, "conversion");
                    serviceHealth.LastReceivedSqsConversions += count;
                }

                // Service-specific SQS traffics
                if (message.Contains("sqs traffic") || message.Contains("traffic processed") ||
                    message.Contains("traffic") && message.Contains("processed"))
                {
                    var count = ExtractCountFromMessage(entry.Message, "traffic");
                    serviceHealth.LastReceivedSqsTraffics += count;
                }
            }

            _logger.LogDebug("Service {ServiceName} business metrics: Keywords={Keywords}, Conversions={Conversions}, Traffics={Traffics}",
                serviceHealth.ServiceName,
                serviceHealth.LastReceivedKeywordReports,
                serviceHealth.LastReceivedSqsConversions,
                serviceHealth.LastReceivedSqsTraffics);
        }

        /// <summary>
        /// NEW: Extract numeric counts from log messages
        /// </summary>
        private int ExtractCountFromMessage(string message, string context)
        {
            if (string.IsNullOrEmpty(message))
                return 1; // Default to 1 if we can't extract

            try
            {
                // Look for patterns like "processed 5 reports", "received 10 conversions", etc.
                var patterns = new[]
                {
                    @"processed\s+(\d+)",
                    @"received\s+(\d+)",
                    @"generated\s+(\d+)",
                    @"(\d+)\s+" + context,
                    @"\b(\d+)\b" // Any number in the message
                };

                foreach (var pattern in patterns)
                {
                    var match = Regex.Match(message, pattern, RegexOptions.IgnoreCase);
                    if (match.Success && int.TryParse(match.Groups[1].Value, out var count))
                    {
                        return Math.Max(1, count); // At least 1
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error extracting count from message: {Message}", message);
            }

            return 1; // Default to 1 for any business activity
        }

        /// <summary>
        /// NEW: Finalize business metrics summary
        /// </summary>
        private void FinalizeBusinessMetricsSummary(SystemHealthSummary healthSummary)
        {
            // No additional processing needed for now
            // This method exists for future enhancements
            _logger.LogDebug("Business metrics finalized: Total Keywords={Keywords}, Conversions={Conversions}, Traffics={Traffics}",
                healthSummary.TotalKeywordReports,
                healthSummary.TotalSqsConversions,
                healthSummary.TotalSqsTraffics);
        }

        /// <summary>
        /// NEW: Enhanced overall status determination including business metrics
        /// </summary>
        private void DetermineOverallStatusWithBusinessMetrics(SystemHealthSummary healthSummary, List<ErrorPattern> allPatterns)
        {
            // Existing status logic first
            bool hasCriticalErrors = allPatterns.Any(p => p.Severity == "Critical" && p.Count > 0);
            bool hasHighSeverityErrors = allPatterns.Any(p => p.Severity == "High" && p.Count > 0);
            bool hasUserImpactingErrors = allPatterns.Any(p => p.IsUserImpacting && p.Count > 0);

            // NEW: Business metrics impact
            var businessIssuesCount = 0;
            if (healthSummary.TotalKeywordReports == 0) businessIssuesCount++;
            if (healthSummary.TotalSqsConversions == 0) businessIssuesCount++;
            if (healthSummary.TotalSqsTraffics == 0) businessIssuesCount++;

            // Calculate percentage of healthy services
            double healthyPercentage = healthSummary.TotalServices > 0 ?
                (double)(healthSummary.HealthyServices + healthSummary.ActiveServices) / healthSummary.TotalServices * 100 : 0;

            // Calculate percentage of running services
            double runningPercentage = healthSummary.TotalServices > 0 ?
                (double)healthSummary.RunningServices / healthSummary.TotalServices * 100 : 0;

            // Enhanced status determination with business metrics
            if (hasCriticalErrors || healthSummary.CriticalServices > 0)
            {
                healthSummary.OverallStatus = "Critical";
            }
            else if (businessIssuesCount >= 3) // All business metrics are zero
            {
                healthSummary.OverallStatus = "Critical";
                _logger.LogWarning("System status set to Critical due to all business metrics being zero");
            }
            else if ((hasHighSeverityErrors && hasUserImpactingErrors) || healthSummary.TotalErrors > 5)
            {
                healthSummary.OverallStatus = "Error";
            }
            else if (businessIssuesCount >= 2) // Two business metrics are zero
            {
                healthSummary.OverallStatus = "Error";
                _logger.LogWarning("System status set to Error due to multiple business metrics being zero");
            }
            else if (hasHighSeverityErrors || healthSummary.TotalErrors > 0)
            {
                healthSummary.OverallStatus = "Warning";
            }
            else if (businessIssuesCount >= 1) // One business metric is zero
            {
                healthSummary.OverallStatus = "Warning";
                _logger.LogWarning("System status set to Warning due to business metric being zero");
            }
            else if (healthSummary.WarningServices > 0 || healthSummary.TotalWarnings > 3)
            {
                healthSummary.OverallStatus = "Warning";
            }
            else if (healthSummary.StaleServices > 0 && healthSummary.StaleServices >= healthSummary.TotalServices / 2)
            {
                healthSummary.OverallStatus = "Stale";
            }
            else if (healthyPercentage >= 90 && businessIssuesCount == 0)
            {
                healthSummary.OverallStatus = "Healthy";
            }
            else if (healthyPercentage >= 70)
            {
                healthSummary.OverallStatus = "Good";
            }
            else if (runningPercentage >= 50)
            {
                healthSummary.OverallStatus = "Degraded";
            }
            else if (healthSummary.StoppedServices == healthSummary.TotalServices)
            {
                healthSummary.OverallStatus = "Stopped";
            }
            else if (healthSummary.InactiveServices > 0 && healthSummary.InactiveServices >= healthSummary.TotalServices / 2)
            {
                healthSummary.OverallStatus = "Inactive";
            }
            else
            {
                healthSummary.OverallStatus = "Unknown";
            }

            _logger.LogInformation("Overall status determined: {Status} (Business issues: {BusinessIssues}/3)",
                healthSummary.OverallStatus, businessIssuesCount);
        }
    }
}