<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
	<?define LSB.LogMonitor.Service_TargetDir=..\LSB.LogMonitor.Service\bin\Debug\net8.0\?>

	<Product Id="*"
			 Name="LSB Log Monitor Service"
			 Language="1033"
			 Codepage="1252"
			 Version="1.0.0.0"
			 Manufacturer="LSB"
			 UpgradeCode="{EB8A2174-7977-45CB-8EDB-936FBC25378E}">
		<Package InstallerVersion="200"
				 Compressed="yes"
				 InstallScope="perMachine"
				 Platform="x64" />
		<Media Id="1" Cabinet="files.cab" EmbedCab="yes" />

		<!-- AUTO UPDATE SUPPORT -->
		<MajorUpgrade DowngradeErrorMessage="A newer version is already installed."
					  Schedule="afterInstallInitialize" />

		<Directory Id="TARGETDIR" Name="SourceDir">
			<Directory Id="ProgramFiles64Folder">
				<Directory Id="CompanyFolder" Name="LSB">
					<Directory Id="INSTALLFOLDER" Name="LogMonitorService" />
				</Directory>
			</Directory>
			<Directory Id="CommonAppDataFolder">
				<Directory Id="LSBDataFolder" Name="LSB">
					<Directory Id="CONFIGFOLDER" Name="LogMonitor" />
				</Directory>
			</Directory>
		</Directory>

		<Feature Id="Complete" Level="1">
			<ComponentGroupRef Id="ServiceFiles" />
			<ComponentGroupRef Id="HarvestedFiles" />
			<ComponentGroupRef Id="ConfigFiles" />
		</Feature>

		<!-- Custom Actions to configure service -->
		<CustomAction Id="SetServiceTimeout"
					  Directory="INSTALLFOLDER"
					  ExeCommand='reg add "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control" /v "ServicesPipeTimeout" /t REG_DWORD /d "180000" /f'
					  Execute="deferred"
					  Impersonate="no" />

		<CustomAction Id="SetServiceConfig"
					  Directory="INSTALLFOLDER"
					  ExeCommand='sc config "LSBLogMonitorService" start= auto obj= LocalSystem'
					  Execute="deferred"
					  Impersonate="no" />

		<CustomAction Id="SetServiceFailureActions"
					  Directory="INSTALLFOLDER"
					  ExeCommand='sc failure "LSBLogMonitorService" reset= 86400 actions= restart/30000/restart/60000/restart/60000'
					  Execute="deferred"
					  Impersonate="no" />

		<CustomAction Id="CreateConfigFolder"
					  Directory="CONFIGFOLDER"
					  ExeCommand='cmd /c "if not exist &quot;C:\ProgramData\LSB\logmonitor.config&quot; echo {} > &quot;C:\ProgramData\LSB\logmonitor.config&quot;"'
					  Execute="deferred"
					  Impersonate="no" />

		<InstallExecuteSequence>
			<Custom Action="CreateConfigFolder" After="InstallFiles">NOT Installed</Custom>
			<Custom Action="SetServiceTimeout" After="InstallServices">NOT Installed</Custom>
			<Custom Action="SetServiceConfig" After="SetServiceTimeout">NOT Installed</Custom>
			<Custom Action="SetServiceFailureActions" After="SetServiceConfig">NOT Installed</Custom>
		</InstallExecuteSequence>
	</Product>

	<!-- Service Files -->
	<Fragment>
		<ComponentGroup Id="ServiceFiles" Directory="INSTALLFOLDER">
			<!-- Main Service Executable -->
			<Component Id="ServiceExe" Guid="{4DD20919-9DCB-4405-88A7-A5A9F57972CD}" Win64="yes">
				<File Id="ServiceExe"
					  Source="$(var.LSB.LogMonitor.Service_TargetDir)LSB.LogMonitor.Service.exe"
					  KeyPath="yes" />

				<!-- Service Installation -->
				<ServiceInstall Id="InstallService"
								Name="LSBLogMonitorService"
								DisplayName="LSB Log Monitor Service"
								Description="LSB Log Monitor Service - Monitors log files and sends notifications"
								Type="ownProcess"
								Start="auto"
								ErrorControl="normal"
								Account="LocalSystem" />

				<ServiceControl Id="ServiceControl"
								Name="LSBLogMonitorService"
								Stop="both"
								Remove="uninstall"
								Wait="yes" />
			</Component>
		</ComponentGroup>
	</Fragment>

	<!-- Configuration Files -->
	<Fragment>
		<ComponentGroup Id="ConfigFiles" Directory="CONFIGFOLDER">
			<Component Id="DefaultConfig" Guid="{6FF20919-9DCB-4405-88A7-A5A9F57972CD}" Win64="yes">
				<File Id="DefaultConfigFile" 
					  Source="default_logmonitor.config" 
					  Name="logmonitor.config"
					  KeyPath="yes" />
			</Component>
		</ComponentGroup>
	</Fragment>
</Wix>
