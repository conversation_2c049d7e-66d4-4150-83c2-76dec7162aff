@echo off
echo Building LSB Log Monitor Service Installer (Simple Version)...

REM Check if WiX is installed
if not exist "%WIX%bin\candle.exe" (
    echo WiX Toolset not found. Please install WiX Toolset v3.11
    echo Download from: https://wixtoolset.org/releases/v3.11/stable
    pause
    exit /b 1
)

REM Build the main service project first
echo Building service project...
cd ..\LSB.LogMonitor.Service
dotnet build -c Release
if %ERRORLEVEL% neq 0 (
    echo Failed to build service project
    pause
    exit /b 1
)

REM Publish the service to ensure all dependencies are included
echo Publishing service...
dotnet publish -c Release -o bin\Release\net8.0\publish --self-contained false
if %ERRORLEVEL% neq 0 (
    echo Failed to publish service project
    pause
    exit /b 1
)

REM Go back to installer directory
cd ..\LSB.LogMonitor.Service.Installer

REM Run Heat to harvest files (excluding main exe and config files)
echo Running Heat to harvest files...
"%WIX%bin\heat.exe" dir "..\LSB.LogMonitor.Service\bin\Release\net8.0\publish" -cg HarvestedFiles -gg -scom -sreg -sfrag -srd -dr INSTALLFOLDER -var var.LSB.LogMonitor.Service.TargetDir -out HarvestedFiles.wxs -t HeatTransform.xslt
if %ERRORLEVEL% neq 0 (
    echo Failed to run Heat
    pause
    exit /b 1
)

REM Build the installer
echo Compiling WiX files...
"%WIX%bin\candle.exe" -dLSB.LogMonitor.Service.TargetDir="..\LSB.LogMonitor.Service\bin\Release\net8.0\publish\" Product.wxs HarvestedFiles.wxs
if %ERRORLEVEL% neq 0 (
    echo Failed to compile WiX files
    pause
    exit /b 1
)

echo Linking installer...
"%WIX%bin\light.exe" -out "LSB.LogMonitor.Service.Installer.msi" Product.wixobj HarvestedFiles.wixobj -ext WixUIExtension
if %ERRORLEVEL% neq 0 (
    echo Failed to link installer
    pause
    exit /b 1
)

echo.
echo ========================================
echo Installer built successfully!
echo File: LSB.LogMonitor.Service.Installer.msi
echo ========================================
echo.
echo You can now install this MSI on other machines.
echo The service will be installed to: C:\Program Files\LSB\LogMonitorService\
echo Configuration will be stored in: C:\ProgramData\LSB\LogMonitor\
echo.
pause
