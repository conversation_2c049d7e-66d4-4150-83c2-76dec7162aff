<?xml version="1.0" encoding="utf-8"?>
<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
                xmlns:wix="http://schemas.microsoft.com/wix/2006/wi">
  
  <!-- Copy everything by default -->
  <xsl:template match="@*|node()">
    <xsl:copy>
      <xsl:apply-templates select="@*|node()"/>
    </xsl:copy>
  </xsl:template>

  <!-- Remove the main executable from harvested files since it's handled separately -->
  <xsl:template match="wix:Component[wix:File/@Source[contains(., 'LSB.LogMonitor.Service.exe')]]" />
  
  <!-- Remove appsettings files from harvested files since they should be handled separately -->
  <xsl:template match="wix:Component[wix:File/@Source[contains(., 'appsettings.json')]]" />
  <xsl:template match="wix:Component[wix:File/@Source[contains(., 'appsettings.Development.json')]]" />
  
  <!-- Remove .pdb files -->
  <xsl:template match="wix:Component[wix:File/@Source[contains(., '.pdb')]]" />
  
  <!-- Remove .xml documentation files -->
  <xsl:template match="wix:Component[wix:File/@Source[contains(., '.xml')]]" />
  
  <!-- Remove any test files -->
  <xsl:template match="wix:Component[wix:File/@Source[contains(., 'test')]]" />
  <xsl:template match="wix:Component[wix:File/@Source[contains(., 'Test')]]" />
  
</xsl:stylesheet>
