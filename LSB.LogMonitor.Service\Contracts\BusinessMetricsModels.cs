﻿using System;
using System.Collections.Generic;

namespace LSB.LogMonitor.Service.Contracts
{
    /// <summary>
    /// <PERSON><PERSON><PERSON> quả check business metrics hàng ngày
    /// </summary>
    public class DailyBusinessMetricsResult
    {
        public DateTime CheckDate { get; set; }
        public List<ClientBusinessMetrics> ClientMetrics { get; set; } = new();
        public bool HasCriticalIssues { get; set; }
        public List<string> CriticalIssues { get; set; } = new();
        public DateTime CheckTime { get; set; }
    }

    /// <summary>
    /// Business metrics của từng client
    /// </summary>
    public class ClientBusinessMetrics
    {
        public string ClientName { get; set; } = string.Empty;

        // 3 metrics chính
        public int KeywordReports { get; set; }
        public int SqsConversions { get; set; }
        public int SqsTraffics { get; set; }

        public bool IsHealthy { get; set; }
        public List<string> Issues { get; set; } = new();
        public DateTime LastDataReceived { get; set; }
    }

    /// <summary>
    /// Model cho client log info sử dụng trong TelegramService
    /// </summary>

}