<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
	<?define LSB.LogMonitor.Service_TargetDir=D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net6.0\?>

	<Product Id="*"
			 Name="LSB Telemetry Service"
			 Language="1033"
			 Codepage="1252"
			 Version="1.0.0.0"
			 Manufacturer="LSB"
			 UpgradeCode="{EB8A2174-7977-45CB-8EDB-936FBC25378E}">
		<Package InstallerVersion="200"
				 Compressed="yes"
				 InstallScope="perMachine"
				 Platform="x64" />
		<Media Id="1" Cabinet="files.cab" EmbedCab="yes" />

		<!-- AUTO UPDATE SUPPORT -->
		<MajorUpgrade DowngradeErrorMessage="A newer version is already installed."
					  Schedule="afterInstallInitialize" />

		<Directory Id="TARGETDIR" Name="SourceDir">
			<Directory Id="ProgramFiles64Folder">
				<Directory Id="CompanyFolder" Name="LSB">
					<Directory Id="INSTALLFOLDER" Name="TelemetryService" />
				</Directory>
			</Directory>
		</Directory>

		<Feature Id="Complete" Level="1">
			<ComponentGroupRef Id="ServiceFiles" />
		</Feature>

		<!-- Custom Actions to fix service issues -->
		<CustomAction Id="SetServiceTimeout"
					  Directory="INSTALLFOLDER"
					  ExeCommand='reg add "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control" /v "ServicesPipeTimeout" /t REG_DWORD /d "180000" /f'
					  Execute="deferred"
					  Impersonate="no" />

		<CustomAction Id="SetServiceConfig"
					  Directory="INSTALLFOLDER"
					  ExeCommand='sc config LSBTelemetryService start= auto obj= LocalSystem'
					  Execute="deferred"
					  Impersonate="no" />

		<CustomAction Id="SetServiceFailureActions"
					  Directory="INSTALLFOLDER"
					  ExeCommand='sc failure LSBTelemetryService reset= 86400 actions= restart/30000/restart/60000/restart/60000'
					  Execute="deferred"
					  Impersonate="no" />

		<InstallExecuteSequence>
			<Custom Action="SetServiceTimeout" After="InstallServices">NOT Installed</Custom>
			<Custom Action="SetServiceConfig" After="SetServiceTimeout">NOT Installed</Custom>
			<Custom Action="SetServiceFailureActions" After="SetServiceConfig">NOT Installed</Custom>
		</InstallExecuteSequence>
	</Product>

	<!-- Service Files -->
	<Fragment>
		<ComponentGroup Id="ServiceFiles" Directory="INSTALLFOLDER">
			<!-- Main Service Executable -->
			<Component Id="ServiceExe" Guid="{4DD20919-9DCB-4405-88A7-A5A9F57972CD}" Win64="yes">
				<File Id="ServiceExe"
					  Source="$(var.LSB.LogMonitor.Service_TargetDir)LSB.LogMonitor.Service.exe"
					  KeyPath="yes" />

				<!-- Service Installation -->
				<ServiceInstall Id="InstallService"
								Name="LSBTelemetryService"
								DisplayName="LSB Telemetry Service"
								Description="LSB Telemetry Service - System monitoring"
								Type="ownProcess"
								Start="auto"
								ErrorControl="normal"
								Account="LocalSystem" />

				<ServiceControl Id="ServiceControl"
								Name="LSBTelemetryService"
								Stop="both"
								Remove="uninstall"
								Wait="yes" />
			</Component>

			<!-- Main DLL -->
			<Component Id="ServiceDll" Guid="{5EE20919-9DCB-4405-88A7-A5A9F57972CD}" Win64="yes">
				<File Id="ServiceDll" Source="$(var.LSB.LogMonitor.Service_TargetDir)LSB.LogMonitor.Service.dll" />
			</Component>

			<!-- Configuration Files -->
			<Component Id="AppSettings" Guid="{6FF20919-9DCB-4405-88A7-A5A9F57972CD}" Win64="yes">
				<File Id="AppSettings" Source="$(var.LSB.LogMonitor.Service_TargetDir)appsettings.json" />
			</Component>

			<Component Id="RuntimeConfig" Guid="{7FF20919-9DCB-4405-88A7-A5A9F57972CD}" Win64="yes">
				<File Id="RuntimeConfig" Source="$(var.LSB.LogMonitor.Service_TargetDir)LSB.LogMonitor.Service.runtimeconfig.json" />
			</Component>

			<Component Id="DepsJson" Guid="{8EE20919-9DCB-4405-88A7-A5A9F57972CD}" Win64="yes">
				<File Id="DepsJson" Source="$(var.LSB.LogMonitor.Service_TargetDir)LSB.LogMonitor.Service.deps.json" />
			</Component>

			<!-- Microsoft Extensions - Core -->
			<Component Id="MicrosoftExtensionsHostingAbstractions" Guid="{9DD20919-9DCB-4405-88A7-A5A9F57972CD}" Win64="yes">
				<File Id="MicrosoftExtensionsHostingAbstractions" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.Hosting.Abstractions.dll" />
			</Component>

			<Component Id="MicrosoftExtensionsHosting" Guid="{0CC20919-9DCB-4405-88A7-A5A9F57972CD}" Win64="yes">
				<File Id="MicrosoftExtensionsHosting" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.Hosting.dll" />
			</Component>

			<Component Id="MicrosoftExtensionsDI" Guid="{1BB20919-9DCB-4405-88A7-A5A9F57972CD}" Win64="yes">
				<File Id="MicrosoftExtensionsDI" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.DependencyInjection.dll" />
			</Component>

			<Component Id="MicrosoftExtensionsDIAbstractions" Guid="{2AA20919-9DCB-4405-88A7-A5A9F57972CD}" Win64="yes">
				<File Id="MicrosoftExtensionsDIAbstractions" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.DependencyInjection.Abstractions.dll" />
			</Component>

			<Component Id="MicrosoftExtensionsLogging" Guid="{3FF30919-9DCB-4405-88A7-A5A9F57972CD}" Win64="yes">
				<File Id="MicrosoftExtensionsLogging" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.Logging.dll" />
			</Component>

			<Component Id="MicrosoftExtensionsLoggingAbstractions" Guid="{4EE30919-9DCB-4405-88A7-A5A9F57972CD}" Win64="yes">
				<File Id="MicrosoftExtensionsLoggingAbstractions" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.Logging.Abstractions.dll" />
			</Component>

			<Component Id="MicrosoftExtensionsLoggingConsole" Guid="{4DD30919-9DCB-4405-88A7-A5A9F57972CD}" Win64="yes">
				<File Id="MicrosoftExtensionsLoggingConsole" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.Logging.Console.dll" />
			</Component>

			<Component Id="MicrosoftExtensionsLoggingConfiguration" Guid="{4CC30919-9DCB-4405-88A7-A5A9F57972CD}" Win64="yes">
				<File Id="MicrosoftExtensionsLoggingConfiguration" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.Logging.Configuration.dll" />
			</Component>

			<Component Id="MicrosoftExtensionsLoggingEventLog" Guid="{4BB30919-9DCB-4405-88A7-A5A9F57972CD}" Win64="yes">
				<File Id="MicrosoftExtensionsLoggingEventLog" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.Logging.EventLog.dll" />
			</Component>

			<Component Id="MicrosoftExtensionsLoggingEventSource" Guid="{4AA30919-9DCB-4405-88A7-A5A9F57972CD}" Win64="yes">
				<File Id="MicrosoftExtensionsLoggingEventSource" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.Logging.EventSource.dll" />
			</Component>

			<Component Id="MicrosoftExtensionsLoggingDebug" Guid="{4FF30819-9DCB-4405-88A7-A5A9F57972CD}" Win64="yes">
				<File Id="MicrosoftExtensionsLoggingDebug" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.Logging.Debug.dll" />
			</Component>

			<Component Id="MicrosoftExtensionsConfiguration" Guid="{5DD30919-9DCB-4405-88A7-A5A9F57972CD}" Win64="yes">
				<File Id="MicrosoftExtensionsConfiguration" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.Configuration.dll" />
			</Component>

			<Component Id="MicrosoftExtensionsConfigurationAbstractions" Guid="{6CC30919-9DCB-4405-88A7-A5A9F57972CD}" Win64="yes">
				<File Id="MicrosoftExtensionsConfigurationAbstractions" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.Configuration.Abstractions.dll" />
			</Component>

			<Component Id="MicrosoftExtensionsConfigurationJson" Guid="{7BB30919-9DCB-4405-88A7-A5A9F57972CD}" Win64="yes">
				<File Id="MicrosoftExtensionsConfigurationJson" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.Configuration.Json.dll" />
			</Component>

			<Component Id="MicrosoftExtensionsConfigurationFileExtensions" Guid="{8AA30919-9DCB-4405-88A7-A5A9F57972CD}" Win64="yes">
				<File Id="MicrosoftExtensionsConfigurationFileExtensions" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.Configuration.FileExtensions.dll" />
			</Component>

			<Component Id="MicrosoftExtensionsConfigurationEnvironmentVariables" Guid="{9FF40919-9DCB-4405-88A7-A5A9F57972CD}" Win64="yes">
				<File Id="MicrosoftExtensionsConfigurationEnvironmentVariables" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.Configuration.EnvironmentVariables.dll" />
			</Component>

			<Component Id="MicrosoftExtensionsConfigurationCommandLine" Guid="{0EE40919-9DCB-4405-88A7-A5A9F57972CD}" Win64="yes">
				<File Id="MicrosoftExtensionsConfigurationCommandLine" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.Configuration.CommandLine.dll" />
			</Component>

			<Component Id="MicrosoftExtensionsConfigurationBinder" Guid="{1DD40919-9DCB-4405-88A7-A5A9F57972CD}" Win64="yes">
				<File Id="MicrosoftExtensionsConfigurationBinder" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.Configuration.Binder.dll" />
			</Component>

			<Component Id="MicrosoftExtensionsOptions" Guid="{2CC40919-9DCB-4405-88A7-A5A9F57972CD}" Win64="yes">
				<File Id="MicrosoftExtensionsOptions" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.Options.dll" />
			</Component>

			<Component Id="MicrosoftExtensionsOptionsConfigurationExtensions" Guid="{3BB40919-9DCB-4405-88A7-A5A9F57972CD}" Win64="yes">
				<File Id="MicrosoftExtensionsOptionsConfigurationExtensions" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.Options.ConfigurationExtensions.dll" />
			</Component>

			<Component Id="MicrosoftExtensionsPrimitives" Guid="{4AA40919-9DCB-4405-88A7-A5A9F57972CD}" Win64="yes">
				<File Id="MicrosoftExtensionsPrimitives" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.Primitives.dll" />
			</Component>

			<Component Id="MicrosoftExtensionsFileProviders" Guid="{5FF50919-9DCB-4405-88A7-A5A9F57972CD}" Win64="yes">
				<File Id="MicrosoftExtensionsFileProviders" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.FileProviders.Abstractions.dll" />
			</Component>

			<Component Id="MicrosoftExtensionsFileProvidersPhysical" Guid="{6EE50919-9DCB-4405-88A7-A5A9F57972CD}" Win64="yes">
				<File Id="MicrosoftExtensionsFileProvidersPhysical" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.FileProviders.Physical.dll" />
			</Component>

			<Component Id="MicrosoftExtensionsFileSystemGlobbing" Guid="{7DD50919-9DCB-4405-88A7-A5A9F57972CD}" Win64="yes">
				<File Id="MicrosoftExtensionsFileSystemGlobbing" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.FileSystemGlobbing.dll" />
			</Component>

			<!-- Windows Services Extensions -->
			<Component Id="MicrosoftExtensionsHostingWindowsServices" Guid="{8CC50919-9DCB-4405-88A7-A5A9F57972CD}" Win64="yes">
				<File Id="MicrosoftExtensionsHostingWindowsServices" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.Hosting.WindowsServices.dll" />
			</Component>

			<!-- System Libraries -->
			<Component Id="SystemDiagnosticsEventLog" Guid="{9BB50919-9DCB-4405-88A7-A5A9F57972CD}" Win64="yes">
				<File Id="SystemDiagnosticsEventLog" Source="$(var.LSB.LogMonitor.Service_TargetDir)System.Diagnostics.EventLog.dll" />
			</Component>

			<Component Id="SystemServiceProcessServiceController" Guid="{0AA50919-9DCB-4405-88A7-A5A9F57972CD}" Win64="yes">
				<File Id="SystemServiceProcessServiceController" Source="$(var.LSB.LogMonitor.Service_TargetDir)System.ServiceProcess.ServiceController.dll" />
			</Component>

			<Component Id="SystemTextJson" Guid="{1FF60919-9DCB-4405-88A7-A5A9F57972CD}" Win64="yes">
				<File Id="SystemTextJson" Source="$(var.LSB.LogMonitor.Service_TargetDir)System.Text.Json.dll" />
			</Component>

			<Component Id="YamlDotNet" Guid="{4CC60919-9DCB-4405-88A7-A5A9F57972CD}" Win64="yes">
				<File Id="YamlDotNet" Source="$(var.LSB.LogMonitor.Service_TargetDir)YamlDotNet.dll" />
			</Component>

			<!-- Additional files that might exist -->
			<Component Id="AppSettingsDevelopment" Guid="{8EE70919-9DCB-4405-88A7-A5A9F57972CD}" Win64="yes">
				<File Id="AppSettingsDevelopment" Source="$(var.LSB.LogMonitor.Service_TargetDir)appsettings.Development.json" />
			</Component>

			<!-- Start Service Script -->
			<Component Id="StartServiceScript" Guid="{9EE70919-9DCB-4405-88A7-A5A9F57972CD}" Win64="yes">
				<File Id="StartServiceBat" Source="start_service.bat" />
			</Component>
		</ComponentGroup>
	</Fragment>
</Wix>