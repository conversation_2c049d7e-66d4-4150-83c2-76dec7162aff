﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using LSB.LogMonitor.Service.Contracts;
using LSB.LogMonitor.Service.Models;
using LSB.LogMonitor.Services;

namespace LSB.LogMonitor.Service.Services
{
    public class TelegramService : ITelegramService
    {
        private readonly ILogger<TelegramService> _logger;
        private readonly HttpClient _httpClient;
        private readonly string _botToken;
        private readonly string _chatId;
        private readonly bool _enabled;
        private readonly bool _includeDetailedErrors;
        private readonly int _maxErrorsPerClient;

        public TelegramService(ILogger<TelegramService> logger, IConfiguration configuration)
        {
            _logger = logger;
            _httpClient = new HttpClient();

            _botToken = configuration.GetValue<string>("Telegram:BotToken") ?? "";
            _chatId = configuration.GetValue<string>("Telegram:MainChatId") ?? ""; // FIXED: Changed from ChatId to MainChatId
            _enabled = configuration.GetValue<bool>("Telegram:Enabled", true);
            _includeDetailedErrors = configuration.GetValue<bool>("Telegram:IncludeDetailedErrors", true);
            _maxErrorsPerClient = configuration.GetValue<int>("Telegram:MaxErrorsPerClient", 5);

            if (string.IsNullOrEmpty(_botToken) || string.IsNullOrEmpty(_chatId))
            {
                _logger.LogWarning("Telegram bot token or chat ID not configured. Notifications will be disabled.");
                _enabled = false;
            }
            else
            {
                _logger.LogInformation("Telegram service initialized successfully. BotToken: {BotToken}, ChatId: {ChatId}, Enabled: {Enabled}",
                    _botToken.Substring(0, 10) + "...", _chatId, _enabled);
            }
        }

        public async Task SendLogNotificationAsync(List<ClientLogInfo> clientsWithLogs, DateTime date, CancellationToken cancellationToken = default)
        {
            if (!_enabled)
            {
                _logger.LogInformation("Telegram notifications are disabled");
                return;
            }

            try
            {
                // OPTION 1: Always send notification (removed error check)
                // Comment out the lines below if you want to always send notifications

                // Check if there are any errors before sending notification
                var hasErrors = clientsWithLogs.Any(c => c.Summary.HealthSummary?.TotalErrors > 0);

                if (!hasErrors)
                {
                    _logger.LogInformation("No errors detected - skipping Telegram notification");
                    return;
                }

                // End of error check - remove this section if you want to always send notifications

                var messages = BuildMultiPartLogSummary(clientsWithLogs, date);

                for (int i = 0; i < messages.Count; i++)
                {
                    await SendMessageAsync(messages[i], cancellationToken);

                    // Add delay between messages to avoid rate limiting
                    if (i < messages.Count - 1)
                        await Task.Delay(1000, cancellationToken);
                }

                _logger.LogInformation("Telegram error alert sent successfully ({PartCount} parts) for {ErrorCount} errors",
                    messages.Count, clientsWithLogs.Sum(c => c.Summary.HealthSummary?.TotalErrors ?? 0));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send Telegram notification");
            }
        }

        public async Task SendTestMessageAsync(CancellationToken cancellationToken = default)
        {
            if (!_enabled)
            {
                _logger.LogWarning("Telegram is not enabled");
                return;
            }

            var testMessage = $"🔧 LSB Log Monitor Service Test\n" +
                            $"⏰ Time: {DateTime.Now:yyyy-MM-dd HH:mm:ss}\n" +
                            $"✅ Service is running correctly!";

            await SendMessageAsync(testMessage, cancellationToken);
        }

        private List<string> BuildMultiPartLogSummary(List<ClientLogInfo> clientsWithLogs, DateTime date)
        {
            var messages = new List<string>();

            // PART 1: Overview - ONLY ERRORS
            var part1 = new StringBuilder();
            part1.AppendLine($"🚨 **LSB Error Alert - {date:yyyy-MM-dd}**");
            part1.AppendLine($"⏰ Generated: {DateTime.Now:HH:mm:ss}");
            part1.AppendLine();

            var hasErrors = false;

            foreach (var client in clientsWithLogs.OrderBy(c => c.ClientName))
            {
                var totalErrors = client.Summary.HealthSummary?.TotalErrors ?? 0;

                // ONLY show if there are errors
                if (totalErrors > 0)
                {
                    hasErrors = true;
                    part1.AppendLine($"🔴 **{client.ClientName}**");
                    part1.AppendLine($"   🚨 **{totalErrors} ERRORS** detected");
                    part1.AppendLine($"   📁 Files: {client.LogFileCount}");
                    part1.AppendLine($"   📝 Total entries: {client.Summary.TotalLogEntries:N0}");

                    if (client.Summary.HealthSummary != null)
                    {
                        var health = client.Summary.HealthSummary;
                        part1.AppendLine($"   🔴 Status: **{health.OverallStatus}**");
                        part1.AppendLine($"   🟢 Running services: {health.RunningServices}/{health.TotalServices}");
                    }
                    part1.AppendLine();
                }
            }

            if (!hasErrors)
            {
                part1.AppendLine("✅ **No errors detected!**");
                part1.AppendLine("All systems are running smoothly.");
                messages.Add(part1.ToString());
                return messages;
            }

            messages.Add(part1.ToString());

            // PART 2: Detailed Error Analysis - ERRORS ONLY
            foreach (var client in clientsWithLogs.OrderBy(c => c.ClientName))
            {
                var totalErrors = client.Summary.HealthSummary?.TotalErrors ?? 0;

                if (totalErrors > 0)
                {
                    var errorMessages = BuildErrorOnlyAnalysis(client, date);
                    messages.AddRange(errorMessages);
                }
            }

            // Add part numbers
            for (int i = 0; i < messages.Count; i++)
            {
                if (messages.Count > 1)
                {
                    messages[i] = $"**[Part {i + 1}/{messages.Count}]**\n\n" + messages[i];
                }
            }

            return messages;
        }

        private List<string> BuildErrorOnlyAnalysis(ClientLogInfo client, DateTime date)
        {
            var messages = new List<string>();
            const int maxLength = 3500;

            var sb = new StringBuilder();
            sb.AppendLine($"🔍 **Error Details - {client.ClientName}**");
            sb.AppendLine($"📅 Date: {date:yyyy-MM-dd}");
            sb.AppendLine();

            // Get ONLY error messages from LogTypeSummaries
            var errorLogType = client.Summary.LogTypeSummaries.FirstOrDefault(s => s.LogType.Equals("Error", StringComparison.OrdinalIgnoreCase));

            if (errorLogType != null && errorLogType.SampleMessages.Any())
            {
                sb.AppendLine($"🚨 **{errorLogType.Count} Error Messages:**");
                sb.AppendLine($"⏰ **Time Range**: {errorLogType.FirstOccurrence:HH:mm:ss} - {errorLogType.LastOccurrence:HH:mm:ss}");
                sb.AppendLine();

                for (int i = 0; i < errorLogType.SampleMessages.Count; i++)
                {
                    var message = errorLogType.SampleMessages[i];
                    if (!string.IsNullOrWhiteSpace(message))
                    {
                        // Clean up the message (remove extra quotes)
                        var cleanMessage = message.Trim().Trim('\'', '"');

                        sb.AppendLine($"**🔴 Error #{i + 1}**");
                        sb.AppendLine($"⏰ {errorLogType.LastOccurrence:yyyy-MM-dd HH:mm:ss}");
                        sb.AppendLine($"💬 **Message**:");
                        sb.AppendLine($"```");
                        sb.AppendLine(FormatErrorMessage(cleanMessage));
                        sb.AppendLine($"```");
                        sb.AppendLine();
                    }

                    // Check if we're approaching the message limit
                    if (sb.Length > maxLength)
                    {
                        messages.Add(sb.ToString());
                        sb.Clear();
                        sb.AppendLine($"🔍 **Error Details Continued - {client.ClientName}**");
                        sb.AppendLine();
                    }
                }
            }

            // Services with ERRORS ONLY
            var health = client.Summary.HealthSummary!;
            var servicesWithErrors = health.Services
                .Where(s => s.ErrorCount > 0)
                .OrderByDescending(s => s.ErrorCount)
                .ToList();

            if (servicesWithErrors.Any())
            {
                sb.AppendLine($"⚠️ **Services with Errors ({servicesWithErrors.Count}):**");
                sb.AppendLine();

                foreach (var service in servicesWithErrors)
                {
                    sb.AppendLine($"🔧 **{service.ServiceName}**");
                    sb.AppendLine($"   🔴 **{service.ErrorCount} Errors**");
                    sb.AppendLine($"   📊 Status: {GetStatusEmoji(service.Status)} {service.Status}");
                    sb.AppendLine($"   🔄 Running: {(service.IsRunning ? "✅ Yes" : "❌ No")}");

                    if (service.LastActivityTime.HasValue)
                    {
                        sb.AppendLine($"   ⏱️ Last Activity: {service.LastActivityTime:yyyy-MM-dd HH:mm:ss}");
                    }

                    sb.AppendLine();
                }
            }

            // Quick Action Summary for ERRORS
            if (health.TotalErrors > 0)
            {
                sb.AppendLine("🚨 **Required Actions:**");
                sb.AppendLine();

                // Analyze common error patterns
                var errorMessages = errorLogType?.SampleMessages ?? new List<string>();

                if (errorMessages.Any(e => e.Contains("Amz Api Settings")))
                {
                    sb.AppendLine("1. ⚙️ **Configure Amazon API Settings**");
                }

                if (errorMessages.Any(e => e.Contains("invocation")))
                {
                    sb.AppendLine("2. 🔧 **Check service configuration and restart services**");
                }

                if (health.Services.Any(s => !s.IsRunning && s.ErrorCount > 0))
                {
                    sb.AppendLine("3. 🔄 **Restart stopped services with errors**");
                }

                sb.AppendLine("4. 📋 **Review detailed error logs for troubleshooting**");
            }

            if (sb.Length > 0)
            {
                messages.Add(sb.ToString());
            }

            return messages;
        }

        private List<string> BuildDetailedErrorAnalysis(ClientLogInfo client, DateTime date)
        {
            var messages = new List<string>();
            const int maxLength = 3500;

            var sb = new StringBuilder();
            sb.AppendLine($"🔍 **Detailed Error Records - {client.ClientName}**");
            sb.AppendLine($"📅 Date: {date:yyyy-MM-dd}");
            sb.AppendLine();

            var health = client.Summary.HealthSummary!;

            // Get ALL error messages from LogTypeSummaries (this contains the actual error messages)
            var errorLogType = client.Summary.LogTypeSummaries.FirstOrDefault(s => s.LogType.Equals("Error", StringComparison.OrdinalIgnoreCase));
            var warningLogType = client.Summary.LogTypeSummaries.FirstOrDefault(s => s.LogType.Equals("Warning", StringComparison.OrdinalIgnoreCase));

            if (errorLogType != null && errorLogType.SampleMessages.Any())
            {
                sb.AppendLine($"📝 **All Error Messages ({errorLogType.Count} total):**");
                sb.AppendLine($"⏰ **Time Range**: {errorLogType.FirstOccurrence:HH:mm:ss} - {errorLogType.LastOccurrence:HH:mm:ss}");
                sb.AppendLine();

                for (int i = 0; i < errorLogType.SampleMessages.Count; i++)
                {
                    var message = errorLogType.SampleMessages[i];
                    if (!string.IsNullOrWhiteSpace(message))
                    {
                        // Clean up the message (remove extra quotes)
                        var cleanMessage = message.Trim().Trim('\'', '"');

                        sb.AppendLine($"**🔴 Error #{i + 1}**");
                        sb.AppendLine($"⏰ **Time**: {errorLogType.LastOccurrence:yyyy-MM-dd HH:mm:ss}");
                        sb.AppendLine($"📄 **Type**: Error");
                        sb.AppendLine($"💬 **Message**:");
                        sb.AppendLine($"```");
                        sb.AppendLine(FormatErrorMessage(cleanMessage));
                        sb.AppendLine($"```");
                        sb.AppendLine();
                    }

                    // Check if we're approaching the message limit
                    if (sb.Length > maxLength)
                    {
                        messages.Add(sb.ToString());
                        sb.Clear();
                        sb.AppendLine($"🔍 **Error Records Continued - {client.ClientName}**");
                        sb.AppendLine();
                    }
                }
            }

            // Add Warning Messages
            if (warningLogType != null && warningLogType.SampleMessages.Any())
            {
                sb.AppendLine($"📝 **All Warning Messages ({warningLogType.Count} total):**");
                sb.AppendLine($"⏰ **Time Range**: {warningLogType.FirstOccurrence:HH:mm:ss} - {warningLogType.LastOccurrence:HH:mm:ss}");
                sb.AppendLine();

                for (int i = 0; i < warningLogType.SampleMessages.Count; i++)
                {
                    var message = warningLogType.SampleMessages[i];
                    if (!string.IsNullOrWhiteSpace(message))
                    {
                        var cleanMessage = message.Trim().Trim('\'', '"');

                        sb.AppendLine($"**🟡 Warning #{i + 1}**");
                        sb.AppendLine($"⏰ **Time**: {warningLogType.LastOccurrence:yyyy-MM-dd HH:mm:ss}");
                        sb.AppendLine($"📄 **Type**: Warning");
                        sb.AppendLine($"💬 **Message**: `{cleanMessage}`");
                        sb.AppendLine();
                    }

                    if (sb.Length > maxLength)
                    {
                        messages.Add(sb.ToString());
                        sb.Clear();
                        sb.AppendLine($"🔍 **Warning Records Continued - {client.ClientName}**");
                        sb.AppendLine();
                    }
                }
            }

            // Service Status Details with recent warnings
            var servicesWithIssues = health.Services
                .Where(s => s.ErrorCount > 0 || s.WarningCount > 0 || s.Status == "Critical")
                .OrderByDescending(s => s.ErrorCount + s.WarningCount)
                .ToList();

            if (servicesWithIssues.Any())
            {
                sb.AppendLine($"⚠️ **Services with Issues ({servicesWithIssues.Count}):**");
                sb.AppendLine();

                foreach (var service in servicesWithIssues)
                {
                    sb.AppendLine($"🔧 **{service.ServiceName}**");
                    sb.AppendLine($"   📊 Status: {GetStatusEmoji(service.Status)} {service.Status}");
                    sb.AppendLine($"   🔴 Error Count: {service.ErrorCount}");
                    sb.AppendLine($"   🟡 Warning Count: {service.WarningCount}");
                    sb.AppendLine($"   ℹ️ Info Count: {service.InfoCount}");

                    if (service.LastActivityTime.HasValue)
                    {
                        sb.AppendLine($"   ⏱️ Last Activity: {service.LastActivityTime:yyyy-MM-dd HH:mm:ss}");
                    }

                    if (service.StartTime.HasValue)
                    {
                        sb.AppendLine($"   🚀 Start Time: {service.StartTime:yyyy-MM-dd HH:mm:ss}");
                    }

                    sb.AppendLine($"   🔄 Is Running: {(service.IsRunning ? "✅ Yes" : "❌ No")}");

                    // Add recent warnings for this service
                    if (service.RecentWarnings.Any())
                    {
                        sb.AppendLine($"   🟡 **Recent Warnings:**");
                        foreach (var warning in service.RecentWarnings.Take(3))
                        {
                            if (!string.IsNullOrWhiteSpace(warning))
                            {
                                sb.AppendLine($"      • `{warning.Trim()}`");
                            }
                        }
                    }

                    sb.AppendLine();

                    if (sb.Length > maxLength)
                    {
                        messages.Add(sb.ToString());
                        sb.Clear();
                        sb.AppendLine($"🔍 **Service Analysis Continued - {client.ClientName}**");
                        sb.AppendLine();
                    }
                }
            }

            // Add Error Pattern Analysis (if available)
            if (health.MostCommonErrors.Any())
            {
                sb.AppendLine($"📊 **Error Pattern Analysis:**");
                sb.AppendLine();

                foreach (var pattern in health.MostCommonErrors.Take(3))
                {
                    var severityEmoji = GetSeverityEmoji(pattern.Severity);
                    sb.AppendLine($"{severityEmoji} **Pattern**: `{pattern.Pattern}`");
                    sb.AppendLine($"📈 **Frequency**: {pattern.Count} occurrences");
                    sb.AppendLine($"⏰ **Time Range**: {pattern.FirstOccurrence:HH:mm} - {pattern.LastOccurrence:HH:mm}");
                    sb.AppendLine($"📖 **Description**: {pattern.Description}");

                    if (!string.IsNullOrEmpty(pattern.RecommendedAction))
                    {
                        sb.AppendLine($"💡 **Recommended Action**: {pattern.RecommendedAction}");
                    }
                    sb.AppendLine("---");
                }
            }

            // Critical Actions Summary
            if (health.TotalErrors > 0 || health.TotalWarnings > 0)
            {
                sb.AppendLine("🚨 **Summary & Recommendations:**");
                sb.AppendLine();

                if (health.TotalErrors > 0)
                {
                    sb.AppendLine($"🔴 **{health.TotalErrors} Errors** detected across {health.Services.Count(s => s.ErrorCount > 0)} services");
                }

                if (health.TotalWarnings > 0)
                {
                    sb.AppendLine($"🟡 **{health.TotalWarnings} Warnings** detected across {health.Services.Count(s => s.WarningCount > 0)} services");
                }

                sb.AppendLine();
                sb.AppendLine("**Immediate Actions:**");
                sb.AppendLine("1. Check Amazon API configuration settings");
                sb.AppendLine("2. Verify service connections and restart stopped services");
                sb.AppendLine("3. Review error logs for system exceptions");

                if (health.Services.Any(s => !s.IsRunning))
                {
                    sb.AppendLine("4. Restart non-running services");
                }
            }

            if (sb.Length > 0)
            {
                messages.Add(sb.ToString());
            }

            return messages;
        }

        private string FormatErrorMessage(string message)
        {
            if (string.IsNullOrWhiteSpace(message))
                return "No message content";

            // Clean up the message
            var cleaned = message.Trim();

            // Handle multi-line YAML content (remove >- prefix)
            if (cleaned.StartsWith(">-"))
            {
                cleaned = cleaned.Substring(2).Trim();
            }

            // Break long lines for better readability
            const int maxLineLength = 80;
            if (cleaned.Length <= maxLineLength)
                return cleaned;

            var lines = new List<string>();
            var words = cleaned.Split(' ');
            var currentLine = new StringBuilder();

            foreach (var word in words)
            {
                if (currentLine.Length + word.Length + 1 > maxLineLength)
                {
                    if (currentLine.Length > 0)
                    {
                        lines.Add(currentLine.ToString());
                        currentLine.Clear();
                    }
                }

                if (currentLine.Length > 0)
                    currentLine.Append(" ");
                currentLine.Append(word);
            }

            if (currentLine.Length > 0)
            {
                lines.Add(currentLine.ToString());
            }

            return string.Join("\n", lines);
        }

        private string GetStatusEmoji(string status)
        {
            return status?.ToLower() switch
            {
                "healthy" => "🟢",
                "good" => "🟢",
                "active" => "🔵",
                "warning" => "🟡",
                "degraded" => "🟠",
                "error" => "🔴",
                "critical" => "🔴",
                "stopped" => "⚫",
                "inactive" => "⚫",
                "stale" => "🟤",
                _ => "⚪"
            };
        }

        private string GetSeverityEmoji(string severity)
        {
            return severity?.ToLower() switch
            {
                "critical" => "🚨",
                "high" => "🔴",
                "medium" => "🟡",
                "low" => "🟢",
                "info" => "ℹ️",
                _ => "⚪"
            };
        }

        private string TruncateMessage(string message, int maxLength)
        {
            if (string.IsNullOrEmpty(message) || message.Length <= maxLength)
                return message ?? "";

            return message.Substring(0, maxLength - 3) + "...";
        }

        private List<string> SplitLongMessage(string message)
        {
            // This method is now replaced by BuildMultiPartLogSummary
            // Keeping for backward compatibility
            const int maxLength = 3800;
            var messages = new List<string>();

            if (message.Length <= maxLength)
            {
                messages.Add(message);
                return messages;
            }

            var lines = message.Split('\n');
            var currentMessage = new StringBuilder();

            foreach (var line in lines)
            {
                if (currentMessage.Length + line.Length + 1 > maxLength)
                {
                    if (currentMessage.Length > 0)
                    {
                        messages.Add(currentMessage.ToString());
                        currentMessage.Clear();
                    }
                }

                currentMessage.AppendLine(line);
            }

            if (currentMessage.Length > 0)
            {
                messages.Add(currentMessage.ToString());
            }

            // Add part numbers
            for (int i = 0; i < messages.Count; i++)
            {
                if (messages.Count > 1)
                {
                    messages[i] = $"**[Part {i + 1}/{messages.Count}]**\n\n" + messages[i];
                }
            }

            return messages;
        }

        private async Task SendMessageAsync(string message, CancellationToken cancellationToken)
        {
            var url = $"https://api.telegram.org/bot{_botToken}/sendMessage";

            var payload = new
            {
                chat_id = _chatId,
                text = message,
                parse_mode = "Markdown",
                disable_web_page_preview = true
            };

            var json = JsonSerializer.Serialize(payload);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync(url, content, cancellationToken);

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
                throw new Exception($"Telegram API error: {response.StatusCode}, {errorContent}");
            }
        }
    }
}