﻿# LSB.LogMonitor.Service - Windows Service Installation Guide

## Prerequisites

- .NET 8 Runtime installed on the target machine.
- Administrator privileges.
- [WiX Toolset](https://wixtoolset.org/) installed (for building the MSI installer).

## Build the Service

1. Build and publish the service project:
   
## Build the MSI Installer

1. Open a command prompt in the `LSB.LogMonitor.Service.Installer` directory.
2. Build the installer:
   
   Or, if using MSBuild:
   
   The resulting MSI will be in the `bin\Release` folder.

## Install the Service

1. Run the generated `.msi` file as Administrator.
2. The service will be installed, set to start automatically, and will run as LocalSystem.

## Uninstall the Service

- Use "Add or Remove Programs" in Windows, or run the MSI installer again and choose "Remove".

## Start/Stop the Service

- Start:
  
- Stop:
  
## Notes

- Ensure configuration files (e.g., `appsettings.json`) are present in the same directory as the executable.
- If you add new dependencies, update `Product.wxs` to include them.
- Check Windows Event Viewer for logs if the service fails to start.