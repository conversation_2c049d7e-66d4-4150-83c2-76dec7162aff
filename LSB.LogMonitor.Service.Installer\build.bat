@echo off
echo Building LSB Log Monitor Service Installer...

REM Build the main service project first
echo Building service project...
cd ..\LSB.LogMonitor.Service
dotnet build -c Release
if %ERRORLEVEL% neq 0 (
    echo Failed to build service project
    pause
    exit /b 1
)

REM Publish the service to ensure all dependencies are included
echo Publishing service...
dotnet publish -c Release -o bin\Release\net8.0\publish
if %ERRORLEVEL% neq 0 (
    echo Failed to publish service project
    pause
    exit /b 1
)

REM Go back to installer directory
cd ..\LSB.LogMonitor.Service.Installer

REM Run Heat to harvest files
echo Running Heat to harvest files...
"%WIX%bin\heat.exe" dir "..\LSB.LogMonitor.Service\bin\Release\net8.0\publish" -cg HarvestedFiles -gg -scom -sreg -sfrag -srd -dr INSTALLFOLDER -var var.LSB.LogMonitor.Service.TargetDir -out HarvestedFiles.wxs -t HeatTransform.xslt
if %ERRORLEVEL% neq 0 (
    echo Failed to run Heat
    pause
    exit /b 1
)

REM Build the installer
echo Building installer...
"%WIX%bin\candle.exe" -dLSB.LogMonitor.Service.TargetDir="..\LSB.LogMonitor.Service\bin\Release\net8.0\publish\" Product.wxs HarvestedFiles.wxs
if %ERRORLEVEL% neq 0 (
    echo Failed to compile WiX files
    pause
    exit /b 1
)

"%WIX%bin\light.exe" -out "LSB.LogMonitor.Service.Installer.msi" Product.wixobj HarvestedFiles.wixobj
if %ERRORLEVEL% neq 0 (
    echo Failed to link installer
    pause
    exit /b 1
)

echo Installer built successfully: LSB.LogMonitor.Service.Installer.msi
pause
