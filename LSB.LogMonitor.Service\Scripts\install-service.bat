﻿@echo off
setlocal enabledelayedexpansion

echo ====================================
echo  LSB Log Monitor Service Installer
echo ====================================
echo.

REM Kiểm tra quyền Administrator
net session >nul 2>&1
if %errorLevel% NEQ 0 (
    echo ERROR: This script requires Administrator privileges!
    echo Please run as Administrator.
    echo.
    pause
    exit /b 1
)

REM Đường dẫn từ Scripts folder đến bin\Release\net8.0
REM Scripts (..) -> bin\Release\net8.0\LSB.LogMonitor.Service.exe
set "SERVICE_PATH=%~dp0..\bin\Release\net8.0\LSB.LogMonitor.Service.exe"
set "SERVICE_NAME=LSB.LogMonitor.Service"
set "SERVICE_DISPLAY_NAME=LSB Log Monitor Service"

echo Script location: %~dp0
echo Service executable path: %SERVICE_PATH%
echo.

REM Resolve đường dẫn tuyệt đối để hiển thị cho user
for %%i in ("%SERVICE_PATH%") do set "FULL_PATH=%%~fi"
echo Resolved full path: %FULL_PATH%
echo.

echo Checking if service executable exists...
if not exist "%SERVICE_PATH%" (
    echo ❌ ERROR: Service executable not found!
    echo Expected location: %SERVICE_PATH%
    echo Resolved path: %FULL_PATH%
    echo.
    echo Please verify:
    echo 1. This script is in the Scripts folder
    echo 2. Project structure is:
    echo    LSB.LogMonitor.Service\
    echo    ├── Scripts\
    echo    │   └── install-service.bat   ← This file
    echo    └── bin\Release\net8.0\
    echo        └── LSB.LogMonitor.Service.exe
    echo.
    echo 3. The project has been built successfully in Release mode
    echo.
    echo Current directory contents:
    echo Scripts folder:
    dir "%~dp0" /B
    echo.
    echo Parent folder:
    dir "%~dp0..\" /B
    echo.
    echo Looking for bin folder:
    if exist "%~dp0..\bin" (
        echo ✓ bin folder exists
        dir "%~dp0..\bin" /B
        if exist "%~dp0..\bin\Release" (
            echo ✓ Release folder exists  
            dir "%~dp0..\bin\Release" /B
            if exist "%~dp0..\bin\Release\net8.0" (
                echo ✓ net8.0 folder exists
                dir "%~dp0..\bin\Release\net8.0" /B
            ) else (
                echo ❌ net8.0 folder not found
            )
        ) else (
            echo ❌ Release folder not found
        )
    ) else (
        echo ❌ bin folder not found
    )
    echo.
    pause
    exit /b 1
)

echo ✓ Service executable found!
echo.
echo File information:
for %%A in ("%SERVICE_PATH%") do (
    echo • Full path: %%~fA
    echo • File size: %%~zA bytes
    echo • Date modified: %%~tA
)
echo.

echo Stopping existing service (if running)...
sc stop "%SERVICE_NAME%" >nul 2>&1
if %errorLevel% EQU 0 (
    echo ✓ Existing service stopped successfully.
    echo Waiting for service to fully stop...
    timeout /t 3 /nobreak >nul
) else (
    echo ℹ No existing service found or service was not running.
)

echo Removing existing service (if exists)...
sc delete "%SERVICE_NAME%" >nul 2>&1
if %errorLevel% EQU 0 (
    echo ✓ Existing service removed successfully.
    timeout /t 2 /nobreak >nul
) else (
    echo ℹ No existing service to remove.
)

echo.
echo Installing new service...
echo Command: sc create "%SERVICE_NAME%" binPath= "%FULL_PATH%" start= auto DisplayName= "%SERVICE_DISPLAY_NAME%"
echo.

REM Sử dụng đường dẫn tuyệt đối cho service creation để tránh lỗi
sc create "%SERVICE_NAME%" binPath= "%FULL_PATH%" start= auto DisplayName= "%SERVICE_DISPLAY_NAME%" type= own

if %errorLevel% EQU 0 (
    echo ✓ Service installed successfully!
    echo.
    
    echo Verifying service configuration...
    sc qc "%SERVICE_NAME%"
    echo.
    
    echo Starting service...
    sc start "%SERVICE_NAME%"
    
    if %errorLevel% EQU 0 (
        echo ✓ Service started successfully!
        echo.
        echo ====================================
        echo ✅ INSTALLATION COMPLETED SUCCESSFULLY!
        echo ====================================
        echo.
        echo Service Details:
        echo • Service Name: %SERVICE_NAME%
        echo • Display Name: %SERVICE_DISPLAY_NAME%
        echo • Executable: %FULL_PATH%
        echo • Status: Running
        echo • Startup Type: Automatic (starts with Windows)
        echo.
        echo Management Commands:
        echo • Open Services Manager: services.msc
        echo • Check service status: sc query "%SERVICE_NAME%"
        echo • Stop service: sc stop "%SERVICE_NAME%"
        echo • Start service: sc start "%SERVICE_NAME%"
        echo • View service config: sc qc "%SERVICE_NAME%"
        echo.
        echo The service will now start automatically when Windows boots.
    ) else (
        echo ⚠ WARNING: Service installed but failed to start!
        echo.
        echo Possible reasons:
        echo • Missing .NET 8.0 Runtime
        echo • Configuration issues in appsettings.json
        echo • Port already in use
        echo • Permission or access issues
        echo • Missing dependencies
        echo.
        echo Troubleshooting steps:
        echo 1. Check Windows Event Viewer:
        echo    - Press Win+R, type 'eventvwr.msc'
        echo    - Navigate to Windows Logs → Application
        echo    - Look for errors related to '%SERVICE_NAME%'
        echo.
        echo 2. Try running the executable directly:
        echo    - Open Command Prompt as Administrator
        echo    - Navigate to: %~dp0..\bin\Release\net8.0\
        echo    - Run: LSB.LogMonitor.Service.exe
        echo.
        echo 3. Check if .NET 8.0 Runtime is installed:
        echo    - Run: dotnet --list-runtimes
        echo.
        echo You can try starting the service manually from Services Manager (services.msc).
    )
) else (
    echo ❌ ERROR: Failed to install service!
    echo Error code: %errorLevel%
    echo.
    echo Common causes:
    echo • Service with the same name already exists
    echo • Invalid executable path or file is corrupted
    echo • Insufficient permissions
    echo • File is locked or in use by another process
    echo • Windows Service Control Manager issues
    echo.
    echo Try running this script as Administrator or restart your computer and try again.
)

echo.
echo Press any key to exit...
pause >nul