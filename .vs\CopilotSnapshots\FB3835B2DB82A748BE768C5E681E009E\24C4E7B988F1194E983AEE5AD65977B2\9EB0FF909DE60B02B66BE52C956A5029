﻿<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">

	<!-- Product Definition -->
	<Product Id="*"
			 Name="LSB Log Monitor Service"
			 Language="1033"
			 Version="*******"
			 Manufacturer="LSB"
			 UpgradeCode="12345678-ABCD-4321-EFAB-123456789ABC">

		<Package InstallerVersion="200"
				 Compressed="yes"
				 InstallScope="perMachine"
				 Description="LSB Log Monitor Service - System Log Monitoring and Telegram Alerts (.NET 8)"
				 Comments="Monitors LSB system logs and sends notifications via Telegram"
				 Manufacturer="LSB" />

		<!-- Handle upgrades -->
		<MajorUpgrade DowngradeErrorMessage="A newer version of LSB Log Monitor Service is already installed." />
		<MediaTemplate EmbedCab="yes" />

		<!-- Properties -->
		<Property Id="MSIUSEREALADMINDETECTION" Value="1" />
		<Property Id="AUTOSTART" Value="1" />

		<!-- Custom Actions -->
		<CustomAction Id="ConfigureServiceDelayedStart"
					  ExeCommand='sc config LSBLogMonitorService start= delayed-auto'
					  Directory="TARGETDIR"
					  Execute="deferred"
					  Impersonate="no"
					  Return="ignore" />

		<CustomAction Id="StartServiceAfterDelay"
					  ExeCommand='sc start LSBLogMonitorService'
					  Directory="TARGETDIR"
					  Execute="deferred"
					  Impersonate="no"
					  Return="ignore" />

		<!-- Install Execute Sequence -->
		<InstallExecuteSequence>
			<Custom Action="ConfigureServiceDelayedStart" After="InstallServices">
				NOT Installed AND NOT REMOVE
			</Custom>
			<Custom Action="StartServiceAfterDelay" After="ConfigureServiceDelayedStart">
				NOT Installed AND NOT REMOVE
			</Custom>
		</InstallExecuteSequence>

		<!-- Main Feature -->
		<Feature Id="ProductFeature"
				 Title="LSB Log Monitor Service"
				 Level="1"
				 Description="Core service for monitoring LSB system logs and sending Telegram notifications">
			<ComponentGroupRef Id="ProductComponents" />
			<ComponentGroupRef Id="ServiceComponents" />
		</Feature>

		<!-- UI -->
		<UI>
			<UIRef Id="WixUI_FeatureTree" />
			<UIRef Id="WixUI_ErrorProgressText" />
		</UI>

		<!-- License file -->
		<WixVariable Id="WixUILicenseRtf" Value="License.rtf" />

	</Product>

	<!-- Directory Structure -->
	<Fragment>
		<Directory Id="TARGETDIR" Name="SourceDir">
			<Directory Id="ProgramFilesFolder">
				<Directory Id="CompanyFolder" Name="LSB">
					<Directory Id="INSTALLFOLDER" Name="LSB Log Monitor Service" />
				</Directory>
			</Directory>
			<Directory Id="CommonAppDataFolder">
				<Directory Id="LSBDataFolder" Name="LSB">
					<Directory Id="ConfigDataFolder" Name="Config" />
					<Directory Id="TelemetryLogsFolder" Name="TelemetryService">
						<Directory Id="ServiceLogsFolder" Name="Logs" />
					</Directory>
					<Directory Id="LSBCentralFolder" Name="LSBCentral" />
				</Directory>
			</Directory>
		</Directory>
	</Fragment>

	<!-- Main Application Components -->
	<Fragment>
		<ComponentGroup Id="ProductComponents" Directory="INSTALLFOLDER">

			<!-- Main Service Executable -->
			<Component Id="MainExecutable" Guid="{11111111-1111-1111-1111-111111111111}">
				<File Id="ServiceExe"
					  Source="$(var.LSB_LogMonitor_Service_TargetDir)LSB.LogMonitor.Service.exe"
					  KeyPath="yes" />
			</Component>

			<!-- Main Service DLL -->
			<Component Id="ServiceLibrary" Guid="{*************-2222-2222-************}">
				<File Source="$(var.LSB_LogMonitor_Service_TargetDir)LSB.LogMonitor.Service.dll" KeyPath="yes" />
			</Component>

			<!-- Microsoft Extensions Dependencies -->
			<Component Id="MicrosoftExtensionsHosting" Guid="{*************-3333-3333-************}">
				<File Source="$(var.LSB_LogMonitor_Service_TargetDir)Microsoft.Extensions.Hosting.dll" KeyPath="yes" />
			</Component>

			<Component Id="MicrosoftExtensionsHostingAbstractions" Guid="{*************-4444-4444-************}">
				<File Source="$(var.LSB_LogMonitor_Service_TargetDir)Microsoft.Extensions.Hosting.Abstractions.dll" KeyPath="yes" />
			</Component>

			<Component Id="MicrosoftExtensionsHostingWindowsServices" Guid="{*************-4545-4545-************}">
				<File Source="$(var.LSB_LogMonitor_Service_TargetDir)Microsoft.Extensions.Hosting.WindowsServices.dll" KeyPath="yes" />
			</Component>

			<Component Id="MicrosoftExtensionsDependencyInjection" Guid="{*************-5555-5555-************}">
				<File Source="$(var.LSB_LogMonitor_Service_TargetDir)Microsoft.Extensions.DependencyInjection.dll" KeyPath="yes" />
			</Component>

			<Component Id="MicrosoftExtensionsDependencyInjectionAbstractions" Guid="{*************-6666-6666-************}">
				<File Source="$(var.LSB_LogMonitor_Service_TargetDir)Microsoft.Extensions.DependencyInjection.Abstractions.dll" KeyPath="yes" />
			</Component>

			<Component Id="MicrosoftExtensionsConfiguration" Guid="{*************-7777-7777-************}">
				<File Source="$(var.LSB_LogMonitor_Service_TargetDir)Microsoft.Extensions.Configuration.dll" KeyPath="yes" />
			</Component>

			<Component Id="MicrosoftExtensionsConfigurationAbstractions" Guid="{*************-8888-8888-************}">
				<File Source="$(var.LSB_LogMonitor_Service_TargetDir)Microsoft.Extensions.Configuration.Abstractions.dll" KeyPath="yes" />
			</Component>

			<Component Id="MicrosoftExtensionsConfigurationJson" Guid="{*************-9999-9999-************}">
				<File Source="$(var.LSB_LogMonitor_Service_TargetDir)Microsoft.Extensions.Configuration.Json.dll" KeyPath="yes" />
			</Component>

			<Component Id="MicrosoftExtensionsLogging" Guid="{AAAAAAAA-AAAA-AAAA-AAAA-AAAAAAAAAAAA}">
				<File Source="$(var.LSB_LogMonitor_Service_TargetDir)Microsoft.Extensions.Logging.dll" KeyPath="yes" />
			</Component>

			<Component Id="MicrosoftExtensionsLoggingAbstractions" Guid="{BBBBBBBB-BBBB-BBBB-BBBB-BBBBBBBBBBBB}">
				<File Source="$(var.LSB_LogMonitor_Service_TargetDir)Microsoft.Extensions.Logging.Abstractions.dll" KeyPath="yes" />
			</Component>

			<Component Id="MicrosoftExtensionsConfigurationBinder" Guid="{CCCCCCCC-CCCC-CCCC-CCCC-CCCCCCCCCCCC}">
				<File Source="$(var.LSB_LogMonitor_Service_TargetDir)Microsoft.Extensions.Configuration.Binder.dll" KeyPath="yes" />
			</Component>

			<Component Id="MicrosoftExtensionsConfigurationCommandLine" Guid="{DDDDDDDD-DDDD-DDDD-DDDD-DDDDDDDDDDDD}">
				<File Source="$(var.LSB_LogMonitor_Service_TargetDir)Microsoft.Extensions.Configuration.CommandLine.dll" KeyPath="yes" />
			</Component>

			<Component Id="MicrosoftExtensionsConfigurationEnvironmentVariables" Guid="{EEEEEEEE-EEEE-EEEE-EEEE-EEEEEEEEEEEE}">
				<File Source="$(var.LSB_LogMonitor_Service_TargetDir)Microsoft.Extensions.Configuration.EnvironmentVariables.dll" KeyPath="yes" />
			</Component>

			<Component Id="MicrosoftExtensionsConfigurationFileExtensions" Guid="{FFFFFFFF-FFFF-FFFF-FFFF-FFFFFFFFFFFF}">
				<File Source="$(var.LSB_LogMonitor_Service_TargetDir)Microsoft.Extensions.Configuration.FileExtensions.dll" KeyPath="yes" />
			</Component>

			<Component Id="MicrosoftExtensionsConfigurationUserSecrets" Guid="{10101010-1010-1010-1010-101010101010}">
				<File Source="$(var.LSB_LogMonitor_Service_TargetDir)Microsoft.Extensions.Configuration.UserSecrets.dll" KeyPath="yes" />
			</Component>

			<Component Id="MicrosoftExtensionsPrimitives" Guid="{*************-2020-2020-************}">
				<File Source="$(var.LSB_LogMonitor_Service_TargetDir)Microsoft.Extensions.Primitives.dll" KeyPath="yes" />
			</Component>

			<Component Id="MicrosoftExtensionsOptions" Guid="{*************-3030-3030-************}">
				<File Source="$(var.LSB_LogMonitor_Service_TargetDir)Microsoft.Extensions.Options.dll" KeyPath="yes" />
			</Component>

			<Component Id="MicrosoftExtensionsFileProvidersAbstractions" Guid="{*************-4040-4040-************}">
				<File Source="$(var.LSB_LogMonitor_Service_TargetDir)Microsoft.Extensions.FileProviders.Abstractions.dll" KeyPath="yes" />
			</Component>

			<Component Id="MicrosoftExtensionsFileProvidersPhysical" Guid="{*************-4646-4646-************}">
				<File Source="$(var.LSB_LogMonitor_Service_TargetDir)Microsoft.Extensions.FileProviders.Physical.dll" KeyPath="yes" />
			</Component>

			<Component Id="MicrosoftExtensionsLoggingConsole" Guid="{*************-4747-4747-************}">
				<File Source="$(var.LSB_LogMonitor_Service_TargetDir)Microsoft.Extensions.Logging.Console.dll" KeyPath="yes" />
			</Component>

			<Component Id="MicrosoftExtensionsLoggingDebug" Guid="{*************-4848-4848-************}">
				<File Source="$(var.LSB_LogMonitor_Service_TargetDir)Microsoft.Extensions.Logging.Debug.dll" KeyPath="yes" />
			</Component>

			<Component Id="MicrosoftExtensionsLoggingEventLog" Guid="{*************-4949-4949-************}">
				<File Source="$(var.LSB_LogMonitor_Service_TargetDir)Microsoft.Extensions.Logging.EventLog.dll" KeyPath="yes" />
			</Component>

			<Component Id="MicrosoftExtensionsOptionsConfigurationExtensions" Guid="{*************-5051-5051-************}">
				<File Source="$(var.LSB_LogMonitor_Service_TargetDir)Microsoft.Extensions.Options.ConfigurationExtensions.dll" KeyPath="yes" />
			</Component>

			<Component Id="MicrosoftExtensionsFileSystemGlobbing" Guid="{*************-5151-5151-************}">
				<File Source="$(var.LSB_LogMonitor_Service_TargetDir)Microsoft.Extensions.FileSystemGlobbing.dll" KeyPath="yes" />
			</Component>

			<Component Id="MicrosoftExtensionsDiagnostics" Guid="{*************-5353-5353-************}">
				<File Source="$(var.LSB_LogMonitor_Service_TargetDir)Microsoft.Extensions.Diagnostics.dll" KeyPath="yes" />
			</Component>

			<Component Id="MicrosoftExtensionsDiagnosticsAbstractions" Guid="{*************-5454-5454-************}">
				<File Source="$(var.LSB_LogMonitor_Service_TargetDir)Microsoft.Extensions.Diagnostics.Abstractions.dll" KeyPath="yes" />
			</Component>

			<!-- System Dependencies -->
			<Component Id="SystemDiagnosticsMetrics" Guid="{*************-5556-5556-************}">
				<File Source="$(var.LSB_LogMonitor_Service_TargetDir)System.Diagnostics.Metrics.dll" KeyPath="yes" />
			</Component>

			<Component Id="SystemDiagnosticsDiagnosticSource" Guid="{*************-5656-5656-************}">
				<File Source="$(var.LSB_LogMonitor_Service_TargetDir)System.Diagnostics.DiagnosticSource.dll" KeyPath="yes" />
			</Component>

			<Component Id="SystemTextJson" Guid="{*************-5757-5757-************}">
				<File Source="$(var.LSB_LogMonitor_Service_TargetDir)System.Text.Json.dll" KeyPath="yes" />
			</Component>

			<Component Id="SystemTextEncodingsWeb" Guid="{*************-5858-5858-************}">
				<File Source="$(var.LSB_LogMonitor_Service_TargetDir)System.Text.Encodings.Web.dll" KeyPath="yes" />
			</Component>

			<Component Id="SystemMemory" Guid="{*************-5959-5959-************}">
				<File Source="$(var.LSB_LogMonitor_Service_TargetDir)System.Memory.dll" KeyPath="yes" />
			</Component>

			<Component Id="SystemRuntimeCompilerServicesUnsafe" Guid="{*************-6061-6061-************}">
				<File Source="$(var.LSB_LogMonitor_Service_TargetDir)System.Runtime.CompilerServices.Unsafe.dll" KeyPath="yes" />
			</Component>

			<Component Id="SystemThreadingTasksExtensions" Guid="{*************-6161-6161-************}">
				<File Source="$(var.LSB_LogMonitor_Service_TargetDir)System.Threading.Tasks.Extensions.dll" KeyPath="yes" />
			</Component>

			<!-- Third Party Dependencies -->
			<Component Id="YamlDotNet" Guid="{*************-6262-6262-************}">
				<File Source="$(var.LSB_LogMonitor_Service_TargetDir)YamlDotNet.dll" KeyPath="yes" />
			</Component>

			<!-- Configuration Files -->
			<Component Id="ConfigTemplate" Guid="{*************-7070-7070-************}">
				<File Id="AppSettingsTemplate"
					  Source="$(var.LSB_LogMonitor_Service_TargetDir)appsettings.json"
					  Name="appsettings.json"
					  KeyPath="yes" />
			</Component>

			<Component Id="DevConfigTemplate" Guid="{*************-7171-7171-************}">
				<File Id="AppSettingsDev"
					  Source="$(var.LSB_LogMonitor_Service_TargetDir)appsettings.Development.json"
					  Name="appsettings.Development.json"
					  KeyPath="yes" />
			</Component>

			<Component Id="RuntimeConfig" Guid="{*************-8080-8080-************}">
				<File Source="$(var.LSB_LogMonitor_Service_TargetDir)LSB.LogMonitor.Service.runtimeconfig.json"
					  KeyPath="yes" />
			</Component>

			<Component Id="DepsJson" Guid="{*************-9090-9090-************}">
				<File Source="$(var.LSB_LogMonitor_Service_TargetDir)LSB.LogMonitor.Service.deps.json"
					  KeyPath="yes" />
			</Component>

		</ComponentGroup>
	</Fragment>

	<!-- Service Registration Components -->
	<Fragment>
		<ComponentGroup Id="ServiceComponents">

			<Component Id="ServiceRegistration" Guid="{B6C7D8E9-BCDE-F123-4567-************}" Directory="INSTALLFOLDER">
				<ServiceInstall Id="LSBLogMonitorServiceInstall"
								Type="ownProcess"
								Name="LSBLogMonitorService"
								DisplayName="LSB Log Monitor Service"
								Description="Monitors LSB system logs and sends alerts via Telegram."
								Start="auto"
								Account="NT AUTHORITY\NetworkService"
								ErrorControl="ignore"
								Interactive="no"
								Vital="no" />

				<ServiceControl Id="LSBLogMonitorServiceControl"
								Start="install"
								Stop="both"
								Remove="uninstall"
								Name="LSBLogMonitorService"
								Wait="no" />

				<RegistryValue Root="HKLM"
							   Key="SOFTWARE\LSB\LSBLogMonitorService"
							   Name="InstallPath"
							   Type="string"
							   Value="[INSTALLFOLDER]"
							   KeyPath="yes" />
			</Component>

			<Component Id="ConfigDirectory" Guid="{E9F0A1B2-EF12-3456-789A-************}" Directory="ConfigDataFolder">
				<CreateFolder />
				<RemoveFolder Id="RemoveConfigFolder" On="uninstall" />
				<RegistryValue Root="HKLM"
							   Key="SOFTWARE\LSB\LSBLogMonitorService"
							   Name="ConfigPath"
							   Type="string"
							   Value="[ConfigDataFolder]"
							   KeyPath="yes" />
			</Component>

			<Component Id="ServiceLogsDirectory" Guid="{F0A1B2C3-F123-4567-89AB-************}" Directory="ServiceLogsFolder">
				<CreateFolder />
				<RemoveFolder Id="RemoveServiceLogsFolder" On="uninstall" />
				<RegistryValue Root="HKLM"
							   Key="SOFTWARE\LSB\LSBLogMonitorService"
							   Name="ServiceLogsPath"
							   Type="string"
							   Value="[ServiceLogsFolder]"
							   KeyPath="yes" />
			</Component>

			<Component Id="LSBCentralDirectory" Guid="{A1B2C3D4-1234-5678-9ABC-************}" Directory="LSBCentralFolder">
				<CreateFolder />
				<RemoveFolder Id="RemoveLSBCentralFolder" On="uninstall" />
				<RegistryValue Root="HKLM"
							   Key="SOFTWARE\LSB\LSBLogMonitorService"
							   Name="LSBCentralPath"
							   Type="string"
							   Value="[LSBCentralFolder]"
							   KeyPath="yes" />
			</Component>

			<Component Id="DefaultConfigCopy" Guid="{E5F6A7B8-5678-9ABC-DEF1-************}" Directory="ConfigDataFolder">
				<File Id="DefaultAppSettings"
					  Source="$(var.LSB_LogMonitor_Service_TargetDir)appsettings.json"
					  Name="appsettings.json"
					  KeyPath="yes" />
			</Component>

		</ComponentGroup>
	</Fragment>

</Wix>