using System;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using LSB.LogMonitor.Service.Models;

namespace LSB.LogMonitor.Services
{
    public class ConfigService : IConfigService
    {
        private readonly ILogger<ConfigService> _logger;
        private readonly string _configFilePath = @"C:\ProgramData\LSB\logmonitor.config";
        private LogMonitorConfig? _cachedConfig;

        public ConfigService(ILogger<ConfigService> logger)
        {
            _logger = logger;
        }

        public async Task<LogMonitorConfig> GetConfigAsync()
        {
            if (_cachedConfig != null)
            {
                return _cachedConfig;
            }

            try
            {
                await EnsureConfigExistsAsync();

                if (File.Exists(_configFilePath))
                {
                    var jsonContent = await File.ReadAllTextAsync(_configFilePath);
                    _cachedConfig = JsonSerializer.Deserialize<LogMonitorConfig>(jsonContent) ?? new LogMonitorConfig();
                    
                    _logger.LogInformation("Config loaded successfully. AccName: {AccName}, MachineName: {MachineName}", 
                        _cachedConfig.AccName, _cachedConfig.MachineName);
                }
                else
                {
                    _cachedConfig = new LogMonitorConfig();
                    _logger.LogWarning("Config file not found, using default config");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading config from {ConfigPath}", _configFilePath);
                _cachedConfig = new LogMonitorConfig();
            }

            return _cachedConfig;
        }

        public async Task<string> GetAccNameAsync()
        {
            var config = await GetConfigAsync();
            
            // Return AccName if available, otherwise fallback to MachineName, then Environment.MachineName
            if (!string.IsNullOrEmpty(config.AccName))
            {
                _logger.LogInformation("Using AccName from config: {AccName}", config.AccName);
                return config.AccName;
            }
            
            if (!string.IsNullOrEmpty(config.MachineName))
            {
                _logger.LogInformation("Using MachineName from config: {MachineName}", config.MachineName);
                return config.MachineName;
            }
            
            var fallbackName = Environment.MachineName;
            _logger.LogWarning("No AccName or MachineName in config, using Environment.MachineName: {MachineName}", fallbackName);
            return fallbackName;
        }

        public async Task SaveConfigAsync(LogMonitorConfig config)
        {
            try
            {
                // Ensure directory exists
                var directory = Path.GetDirectoryName(_configFilePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                    _logger.LogInformation("Created config directory: {Directory}", directory);
                }

                config.LastUpdated = DateTime.Now;
                var jsonContent = JsonSerializer.Serialize(config, new JsonSerializerOptions 
                { 
                    WriteIndented = true 
                });
                
                await File.WriteAllTextAsync(_configFilePath, jsonContent);
                _cachedConfig = config; // Update cache
                
                _logger.LogInformation("Config saved successfully to {ConfigPath}", _configFilePath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving config to {ConfigPath}", _configFilePath);
                throw;
            }
        }

        public async Task EnsureConfigExistsAsync()
        {
            try
            {
                if (!File.Exists(_configFilePath))
                {
                    _logger.LogInformation("Config file not found, creating default config at {ConfigPath}", _configFilePath);
                    
                    var defaultConfig = new LogMonitorConfig
                    {
                        AccName = Environment.MachineName, // Default to machine name
                        MachineName = Environment.MachineName,
                        LastUpdated = DateTime.Now,
                        IsEnabled = true,
                        Version = "1.0"
                    };
                    
                    await SaveConfigAsync(defaultConfig);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error ensuring config exists at {ConfigPath}", _configFilePath);
            }
        }
    }
}
