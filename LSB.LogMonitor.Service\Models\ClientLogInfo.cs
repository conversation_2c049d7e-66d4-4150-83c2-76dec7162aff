﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using LSB.LogMonitor.Service.Contracts;

namespace LSB.LogMonitor.Service.Models
{
    public class ClientLogInfo
    {
        public string ClientName { get; set; } = string.Empty;
        public int LogFileCount { get; set; }
        public LogSummaryResponse Summary { get; set; } = new();
        public DateTime Date { get; set; }
    }
}
