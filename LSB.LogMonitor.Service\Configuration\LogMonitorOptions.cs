using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace LSB.LogMonitor.Service.Configuration
{
    public class LogMonitorOptions
    {
        public const string SectionName = "LogMonitor";

        public double CheckIntervalHours { get; set; } = 1.0;
        public string LogRootPath { get; set; } = @"C:\ProgramData\DaBox\LSBHub\Logs";
    }
}
