# LSB Log Monitor Service Installer

Installer WiX cho LSB Log Monitor Service sử dụng WiX ToolSet để tạo Windows Service installer.

## Yêu cầu

1. **WiX Toolset v3.11** - Download từ: https://wixtoolset.org/releases/v3.11/stable
2. **.NET 8.0 SDK** - Đ<PERSON> build service project
3. **Visual Studio** (t<PERSON><PERSON>) - <PERSON><PERSON> build từ IDE

## Cách sử dụng

### Phương pháp 1: Build bằng Batch Script (Khuyến nghị)

1. Mở Command Prompt với quyền Administrator
2. Chạy script build:
   ```cmd
   cd LSB.LogMonitor.Service.Installer
   build_simple.bat
   ```

### Phương pháp 2: Build bằng Visual Studio

1. Mở solution `LSB.LogMonitor.Service.sln`
2. Build project `LSB.LogMonitor.Service` ở mode Release
3. Build project `LSB.LogMonitor.Service.Installer`

## Cấu trúc Installer

### Files được cài đặt:
- **Service executable**: `C:\Program Files\LSB\LogMonitorService\`
- **Dependencies**: Tất cả DLL dependencies được tự động harvest bằng WiX Heat tool
- **Configuration**: `C:\ProgramData\LSB\LogMonitor\logmonitor.config`

### Windows Service:
- **Service Name**: `LSBLogMonitorService`
- **Display Name**: `LSB Log Monitor Service`
- **Start Type**: Automatic
- **Account**: LocalSystem
- **Failure Actions**: Restart service sau 30s, 60s, 60s

### Custom Actions:
1. Tạo config folder và default config file
2. Set service timeout registry
3. Configure service start type và account
4. Set service failure recovery actions

## Tính năng

- ✅ **Auto-harvest dependencies**: Sử dụng WiX Heat tool để tự động include tất cả DLL
- ✅ **Major upgrade support**: Tự động uninstall version cũ khi install version mới
- ✅ **Service configuration**: Tự động cấu hình Windows Service
- ✅ **Config management**: Tạo default config file nếu chưa tồn tại
- ✅ **Clean uninstall**: Xóa service và files khi uninstall

## Troubleshooting

### Lỗi "WiX Toolset not found"
- Cài đặt WiX Toolset v3.11 từ link trên
- Restart Command Prompt sau khi cài đặt

### Lỗi "Failed to build service project"
- Đảm bảo .NET 8.0 SDK đã được cài đặt
- Chạy `dotnet --version` để kiểm tra

### Lỗi "Failed to run Heat"
- Đảm bảo service project đã được build thành công
- Kiểm tra đường dẫn publish folder tồn tại

## Cài đặt trên máy khác

1. Copy file `LSB.LogMonitor.Service.Installer.msi` sang máy đích
2. Chạy với quyền Administrator:
   ```cmd
   msiexec /i LSB.LogMonitor.Service.Installer.msi
   ```
3. Service sẽ tự động start sau khi cài đặt

## Gỡ cài đặt

```cmd
msiexec /x LSB.LogMonitor.Service.Installer.msi
```

Hoặc qua Control Panel > Programs and Features
