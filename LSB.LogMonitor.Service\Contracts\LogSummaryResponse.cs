﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace LSB.LogMonitor.Service.Contracts
{
    public class LogSummaryResponse
    {
        public string ClientName { get; set; } = string.Empty;
        public DateTime Date { get; set; }
        public int TotalLogEntries { get; set; }
        public List<LogSummary> LogTypeSummaries { get; set; } = new();
        public List<LogFileInfo> LogFiles { get; set; } = new();
        public Dictionary<string, int> LogTypeDistribution { get; set; } = new();
        public Dictionary<string, int> SourceNameDistribution { get; set; } = new();
        public SystemHealthSummary? HealthSummary { get; set; }
    }

    public class LogSummary
    {
        public string LogType { get; set; } = string.Empty;
        public int Count { get; set; }
        public DateTime FirstOccurrence { get; set; }
        public DateTime LastOccurrence { get; set; }
        public List<string> SampleMessages { get; set; } = new();
    }

    public class LogFileInfo
    {
        public string FileName { get; set; } = string.Empty;
        public int TotalEntries { get; set; }
        public DateTime? FirstEntryTime { get; set; }
        public DateTime? LastEntryTime { get; set; }
    }

    public class SystemHealthSummary
    {
        // Existing properties (giữ nguyên tất cả)
        public int TotalServices { get; set; }
        public int RunningServices { get; set; }
        public int StoppedServices { get; set; }
        public int IdleServices { get; set; }
        public int StaleServices { get; set; }
        public int ActiveServices { get; set; }
        public int InactiveServices { get; set; }
        public int CriticalServices { get; set; }
        public int WarningServices { get; set; }
        public int HealthyServices { get; set; }
        public bool AllServicesRunning { get; set; }
        public int TotalErrors { get; set; }
        public int TotalWarnings { get; set; }
        public DateTime LastUpdated { get; set; }
        public string OverallStatus { get; set; } = string.Empty;
        public Dictionary<string, int> ServiceStatusDistribution { get; set; } = new();
        public Dictionary<string, DateTime?> LastActivityByService { get; set; } = new();
        public int TotalUniqueErrorPatterns { get; set; }
        public List<ErrorPattern> MostCommonErrors { get; set; } = new();
        public List<ErrorPattern> MostSevereErrors { get; set; } = new();
        public List<ErrorPattern> UserImpactingErrors { get; set; } = new();
        public List<ServiceHealthInfo> Services { get; set; } = new();

        // NEW: Senior Priority Metrics
        // 1. TỐC ĐỘ LỖI - Phát hiện sự cố sớm
        public double ErrorsPerMinute { get; set; }
        public bool IsErrorSpike { get; set; } // >10 lỗi trong 5 phút
        public DateTime? LastErrorSpikeTime { get; set; }

        // 2. TÌNH TRẠNG DỊCH VỤ - Đo SLA  
        public double ServiceUptimePercentage { get; set; }
        public TimeSpan CurrentDowntime { get; set; }

        // 3. LOG MỚI NHẤT - Phát hiện dịch vụ "câm"
        public TimeSpan TimeSinceLastLog { get; set; }
        public bool IsLogStale { get; set; } // >30 phút không có log

        // NEW: 3 Business Metrics được thêm
        public int TotalKeywordReports { get; set; }
        public int TotalSqsConversions { get; set; }
        public int TotalSqsTraffics { get; set; }

        // NEW: Supporting business metrics properties
        public List<string> MissingBusinessDataServices { get; set; } = new();

        // FIX: Thay computed property bằng normal property + method
        public bool RequiresImmediateAttention { get; set; }

        // Helper method để tính RequiresImmediateAttention
        public void CalculateRequiresImmediateAttention()
        {
            RequiresImmediateAttention =
                IsErrorSpike ||
                TimeSinceLastLog.TotalMinutes > 30 ||
                ServiceUptimePercentage < 95 ||
                CurrentDowntime.TotalMinutes > 5 ||
                // Business critical conditions
                (TotalKeywordReports == 0 && TotalSqsConversions == 0) ||
                (TotalKeywordReports == 0 && TotalSqsTraffics == 0) ||
                (TotalSqsConversions == 0 && TotalSqsTraffics == 0) ||
                MissingBusinessDataServices.Count >= 2;
        }
    }

    public class ServiceHealthInfo
    {
        // Existing properties (giữ nguyên tất cả)
        public string ServiceName { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public bool IsRunning { get; set; }
        public DateTime? StartTime { get; set; }
        public DateTime? LastActivityTime { get; set; }
        public int ErrorCount { get; set; }
        public int WarningCount { get; set; }
        public int InfoCount { get; set; }
        public List<string> RecentErrors { get; set; } = new();
        public List<string> RecentWarnings { get; set; } = new();
        public List<ErrorPattern> CommonErrorPatterns { get; set; } = new();

        // NEW: Per-service Senior Metrics
        public double ServiceErrorsPerMinute { get; set; }
        public TimeSpan ServiceTimeSinceLastLog { get; set; }
        public bool IsServiceStale { get; set; }
        public double ServiceUptimePercentage { get; set; }

        // NEW: 3 Business Metrics được thêm
        public int LastReceivedKeywordReports { get; set; }
        public int LastReceivedSqsConversions { get; set; }
        public int LastReceivedSqsTraffics { get; set; }
    }

    public class ErrorPattern
    {
        public string Pattern { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public int Count { get; set; }
        public string Severity { get; set; } = string.Empty;
        public DateTime FirstOccurrence { get; set; }
        public DateTime LastOccurrence { get; set; }
        public List<string> SampleMessages { get; set; } = new();
        public bool IsUserImpacting { get; set; }
        public string RecommendedAction { get; set; } = string.Empty;
    }
}